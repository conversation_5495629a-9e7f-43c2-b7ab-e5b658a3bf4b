PyCharm调试配置完整指南
========================

## 方法1: 使用虚拟环境Python解释器（推荐）

### Python解释器配置：
1. 打开PyCharm设置 (Preferences/Settings)
2. 导航到: Project: CuraProject → Python Interpreter
3. 点击齿轮图标 → Add...
4. 选择: Existing environment
5. 解释器路径: /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/cura_venv/bin/python

### 运行配置：
- 配置名称: Cura Debug (Virtual Env)
- 脚本路径: /Users/<USER>/PycharmProjects/CuraProject/direct_debug.py
- 工作目录: /Users/<USER>/PycharmProjects/CuraProject
- 环境变量: PYTHONUNBUFFERED=1

## 方法2: 使用系统Python解释器

### Python解释器配置：
1. 打开PyCharm设置 (Preferences/Settings)
2. 导航到: Project: CuraProject → Python Interpreter
3. 点击齿轮图标 → Add...
4. 选择: System Interpreter
5. 解释器路径: /opt/homebrew/bin/python3.12

### 运行配置：
- 配置名称: Cura Debug (System)
- 脚本路径: /Users/<USER>/PycharmProjects/CuraProject/pycharm_debug.py
- 工作目录: /Users/<USER>/PycharmProjects/CuraProject
- 环境变量: PYTHONUNBUFFERED=1

## 调试技巧：
1. 在代码中设置断点
2. 使用Debug模式运行（绿色虫子图标）
3. 可以检查变量值、调用栈等
4. 支持步进、步过、步出等调试操作

## 故障排除：
- 如果遇到模块导入错误，检查Python解释器配置
- 如果遇到段错误，尝试使用方法2（系统Python）
- 确保所有依赖都已正确安装
