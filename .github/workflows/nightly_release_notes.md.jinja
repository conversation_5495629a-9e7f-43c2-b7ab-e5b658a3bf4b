# Nightlies

> :clock12: Created at: {{ timestamp }}

|               |                                                                                            |
|--------------:|--------------------------------------------------------------------------------------------|
| **Nightlies** | [![nightly](https://github.com/Ultimaker/Cura/actions/workflows/{{ caller_workflow }}/badge.svg{{ branch_specific }}?event=schedule)](https://github.com/Ultimaker/Cura/actions/workflows/installers.yml) |

# Unit Test results

|                                |                                                                                                                                                                                                                               |
|-------------------------------:|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|                   **Cura** | [![unit-test](https://github.com/Ultimaker/Cura/actions/workflows/unit-test.yml/badge.svg{{ branch_specific }})](https://github.com/Ultimaker/Cura/actions/workflows/unit-test.yml)                                                                |
|             **CuraEngine** | [![unit-test](https://github.com/Ultimaker/CuraEngine/actions/workflows/unit-test.yml/badge.svg{{ branch_specific }})](https://github.com/Ultimaker/CuraEngine/actions/workflows/unit-test.yml)                                                    |
|                **Uranium** | [![unit-test](https://github.com/Ultimaker/Uranium/actions/workflows/unit-test.yml/badge.svg{{ branch_specific }})](https://github.com/Ultimaker/Uranium/actions/workflows/unit-test.yml)                                                          |
| **CuraEngine GradualFlow** | [![unit-test](https://github.com/Ultimaker/CuraEngine_plugin_gradual_flow/actions/workflows/unit-test.yml/badge.svg)](https://github.com/Ultimaker/CuraEngine_plugin_gradual_flow/actions/workflows/unit-test.yml) |
|  **synsepalum-dulcificum** | [![unit-test](https://github.com/Ultimaker/synsepalum-dulcificum/actions/workflows/unit-test.yml/badge.svg)](https://github.com/Ultimaker/synsepalum-dulcificum/actions/workflows/unit-test.yml)                   |
|                 **libSavitar** | [![unit-test](https://github.com/Ultimaker/libSavitar/actions/workflows/unit-test.yml/badge.svg)](https://github.com/Ultimaker/libSavitar/actions/workflows/unit-test.yml)                                                    |
|                  **libnest2d** | [![unit-test](https://github.com/Ultimaker/libnest2d/actions/workflows/unit-test.yml/badge.svg)](https://github.com/Ultimaker/libnest2d/actions/workflows/unit-test.yml)                                                      |

# Conan packages

|                                     |                                                                                                                                                                                                                                           |
|------------------------------------:|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|                        **Cura** | [![conan-package](https://github.com/Ultimaker/Cura/actions/workflows/conan-package.yml/badge.svg{{ branch_specific }})](https://github.com/Ultimaker/Cura/actions/workflows/conan-package.yml)                                                     |
|                  **CuraEngine** | [![conan-package](https://github.com/Ultimaker/CuraEngine/actions/workflows/package.yml/badge.svg{{ branch_specific }})](https://github.com/Ultimaker/CuraEngine/actions/workflows/package.yml)                                         |
|                     **Uranium** | [![conan-package](https://github.com/Ultimaker/Uranium/actions/workflows/conan-package.yml/badge.svg{{ branch_specific }})](https://github.com/Ultimaker/Uranium/actions/workflows/conan-package.yml)                                               |
|               **fdm_materials** | [![conan-package](https://github.com/Ultimaker/fdm_materials/actions/workflows/conan-package.yml/badge.svg{{ branch_specific }})](https://github.com/Ultimaker/fdm_materials/actions/workflows/conan-package.yml)                                   |
|            **cura-binary-data** | [![conan-package](https://github.com/Ultimaker/cura-binary-data/actions/workflows/conan-package.yml/badge.svg{{ branch_specific }})](https://github.com/Ultimaker/cura-binary-data/actions/workflows/conan-package.yml)                             |
|      **CuraEngine GradualFlow** | [![conan-package](https://github.com/Ultimaker/CuraEngine_plugin_gradual_flow/actions/workflows/conan-package.yml/badge.svg)](https://github.com/Ultimaker/CuraEngine_plugin_gradual_flow/actions/workflows/conan-package.yml) |
|       **synsepalum-dulcificum** | [![conan-package](https://github.com/Ultimaker/synsepalum-dulcificum/actions/workflows/conan-package.yml/badge.svg)](https://github.com/Ultimaker/synsepalum-dulcificum/actions/workflows/conan-package.yml)                   |
| **CuraEngine gRPC definitions** | [![conan-package](https://github.com/Ultimaker/CuraEngine_grpc_definitions/actions/workflows/conan-package.yml/badge.svg)](https://github.com/Ultimaker/CuraEngine_grpc_definitions/actions/workflows/conan-package.yml)       |
|                        **libArcus** | [![conan-package](https://github.com/Ultimaker/libArcus/actions/workflows/conan-package.yml/badge.svg)](https://github.com/Ultimaker/libArcus/actions/workflows/conan-package.yml)                                                        |
|                         **pyArcus** | [![conan-package](https://github.com/Ultimaker/pyArcus/actions/workflows/conan-package.yml/badge.svg)](https://github.com/Ultimaker/pyArcus/actions/workflows/conan-package.yml)                                                          |
|                      **libSavitar** | [![conan-package](https://github.com/Ultimaker/libSavitar/actions/workflows/conan-package.yml/badge.svg)](https://github.com/Ultimaker/libSavitar/actions/workflows/conan-package.yml)                                                    |
|                       **pySavitar** | [![conan-package](https://github.com/Ultimaker/pySavitar/actions/workflows/conan-package.yml/badge.svg)](https://github.com/Ultimaker/pySavitar/actions/workflows/conan-package.yml)                                                      |
|                       **libnest2d** | [![conan-package](https://github.com/Ultimaker/libnest2d/actions/workflows/conan-package.yml/badge.svg)](https://github.com/Ultimaker/libnest2d/actions/workflows/conan-package.yml)                                                      |
|                        **pynest2d** | [![conan-package](https://github.com/Ultimaker/pynest2d/actions/workflows/conan-package.yml/badge.svg)](https://github.com/Ultimaker/pynest2d/actions/workflows/conan-package.yml)                                                        |
