#!/usr/bin/env python3

"""
直接调试脚本
直接在虚拟环境中运行，避免兼容性问题
"""

import os
import sys

# 添加必要的路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
cura_dir = os.path.join(project_root, "Cura")

# 设置工作目录
os.chdir(cura_dir)

# 设置环境变量
os.environ["QT_QUICK_FLICKABLE_WHEEL_DECELERATION"] = "5000"
if sys.platform != "linux":
    os.environ["QT_PLUGIN_PATH"] = ""
    os.environ["QML2_IMPORT_PATH"] = ""
    os.environ["QT_OPENGL_DLL"] = ""

# 添加调试参数
if "--debug" not in sys.argv:
    sys.argv.append("--debug")

print("=== 直接调试模式 ===")
print(f"工作目录: {os.getcwd()}")
print(f"Python版本: {sys.version}")
print(f"命令行参数: {sys.argv}")

# 直接导入并运行cura_app
try:
    # 这里我们直接执行cura_app.py的内容
    cura_app_path = os.path.join(cura_dir, "cura_app.py")
    with open(cura_app_path, 'r', encoding='utf-8') as f:
        code = f.read()
    
    # 执行代码
    exec(code, {'__file__': cura_app_path})
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
