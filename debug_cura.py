#!/usr/bin/env python3

"""
PyCharm调试启动脚本
用于在PyCharm IDE中调试Cura应用程序
"""

import os
import sys

def setup_python_path():
    """设置Python路径"""
    project_root = os.path.dirname(os.path.abspath(__file__))

    # 需要添加到Python路径的目录
    paths_to_add = [
        # Cura虚拟环境的site-packages
        os.path.join(project_root, "Cura", "build", "generators", "cura_venv", "lib", "python3.12", "site-packages"),
        # Uranium包路径（从Conan）
        "/Users/<USER>/.conan2/p/urani216ac7fb1cb90/p/site-packages",
        # pyArcus模块路径
        "/Users/<USER>/.conan2/p/b/pyarc0bab8c65eb3af/p/lib",
        # Cura源码目录
        os.path.join(project_root, "Cura"),
        # Uranium源码目录
        os.path.join(project_root, "Uranium"),
    ]

    # 添加到sys.path的开头，确保优先级
    for path in reversed(paths_to_add):
        if os.path.exists(path) and path not in sys.path:
            sys.path.insert(0, path)

    # 也设置PYTHONPATH环境变量
    current_pythonpath = os.environ.get("PYTHONPATH", "")
    all_paths = paths_to_add + ([current_pythonpath] if current_pythonpath else [])
    os.environ["PYTHONPATH"] = os.pathsep.join(all_paths)

    print(f"Python路径已设置:")
    for i, path in enumerate(sys.path[:10]):  # 只显示前10个
        print(f"  {i}: {path}")

def main():
    """主函数"""
    print("=== Cura PyCharm 调试启动器 ===")

    # 设置Python路径
    setup_python_path()

    # 获取项目根目录
    project_root = os.path.dirname(os.path.abspath(__file__))
    cura_dir = os.path.join(project_root, "Cura")

    # 设置工作目录为Cura目录
    os.chdir(cura_dir)
    print(f"工作目录: {os.getcwd()}")

    # 设置环境变量
    os.environ["QT_QUICK_FLICKABLE_WHEEL_DECELERATION"] = "5000"
    if sys.platform != "linux":
        os.environ["QT_PLUGIN_PATH"] = ""
        os.environ["QML2_IMPORT_PATH"] = ""
        os.environ["QT_OPENGL_DLL"] = ""

    # 添加调试参数
    if "--debug" not in sys.argv:
        sys.argv.append("--debug")

    print(f"命令行参数: {sys.argv}")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")

    # 测试关键模块导入
    try:
        print("测试模块导入...")
        import PyQt6
        print("✓ PyQt6 导入成功")
        import UM
        print("✓ UM (Uranium) 导入成功")
        import cura
        print("✓ cura 导入成功")
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return

    # 导入并启动Cura
    try:
        print("正在启动Cura...")

        # 直接执行cura_app.py
        cura_app_path = os.path.join(cura_dir, "cura_app.py")
        if os.path.exists(cura_app_path):
            # 使用exec执行cura_app.py
            with open(cura_app_path, 'r', encoding='utf-8') as f:
                code = f.read()
            exec(code, {'__file__': cura_app_path})
        else:
            print(f"找不到cura_app.py文件: {cura_app_path}")
            sys.exit(1)

    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已正确安装所有依赖项")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    except Exception as e:
        print(f"启动错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
