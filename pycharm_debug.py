#!/usr/bin/env python3

"""
PyCharm调试启动脚本 - 简化版本
用于在PyCharm IDE中调试Cura应用程序
"""

import os
import sys
import subprocess

def main():
    """主函数 - 使用虚拟环境启动Cura"""
    print("=== Cura PyCharm 调试启动器（简化版）===")
    
    # 获取项目根目录
    project_root = os.path.dirname(os.path.abspath(__file__))
    cura_dir = os.path.join(project_root, "Cura")
    
    # 虚拟环境路径
    venv_python = os.path.join(cura_dir, "build", "generators", "cura_venv", "bin", "python")
    
    # 检查虚拟环境是否存在
    if not os.path.exists(venv_python):
        print(f"错误: 找不到虚拟环境Python: {venv_python}")
        print("请先运行Conan安装来创建虚拟环境")
        sys.exit(1)
    
    # 设置环境变量
    env = os.environ.copy()
    env["QT_QUICK_FLICKABLE_WHEEL_DECELERATION"] = "5000"
    if sys.platform != "linux":
        env["QT_PLUGIN_PATH"] = ""
        env["QML2_IMPORT_PATH"] = ""
        env["QT_OPENGL_DLL"] = ""
    
    # 构建命令
    cmd = [venv_python, "cura_app.py", "--debug"]
    
    print(f"工作目录: {cura_dir}")
    print(f"Python解释器: {venv_python}")
    print(f"命令: {' '.join(cmd)}")
    print("正在启动Cura...")
    
    try:
        # 使用subprocess启动Cura，这样可以在PyCharm中调试
        os.chdir(cura_dir)
        result = subprocess.run(cmd, env=env, cwd=cura_dir)
        sys.exit(result.returncode)
    except KeyboardInterrupt:
        print("\n用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"启动错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
