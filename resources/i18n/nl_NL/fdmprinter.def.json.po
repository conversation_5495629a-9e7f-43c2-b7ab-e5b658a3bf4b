msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-03-13 09:02+0100\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: nl_NL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"

msgctxt "prime_tower_mode description"
msgid "<html>How to generate the prime tower:<ul><li><b>Normal:</b> create a bucket in which secondary materials are primed</li><li><b>Interleaved:</b> create a prime tower as sparse as possible. This will save time and filament, but is only possible if the used materials adhere to each other</li></ul></html>"
msgstr "<html>Zo genereer je de voorbereidingstoren:<ul><li><b>Normaal:</b> creëer een emmer waarin secundaire materialen worden voorbereid</li><li><b>Interleaved:</b> creëer een zo minimaal mogelijke voorbereidingstoren. Dit bespaart tijd en filament, maar is alleen mogelijk als de gebruikte materialen aan elkaar hechten</li></ul></html>"

msgctxt "brim_inside_margin description"
msgid "A brim around a model may touch an other model where you don't want it. This removes all brim within this distance from brimless models."
msgstr "Een rand rond een model kan een ander model raken op een plek waarvan u dat niet wilt. Dit verwijdert alle rand binnen deze afstand van modellen zonder rand."

msgctxt "ironing_inset description"
msgid "A distance to keep from the edges of the model. Ironing all the way to the edge of the mesh may result in a jagged edge on your print."
msgstr "De afstand die moet worden aangehouden tot de randen van het model. Strijken tot de rand van het raster kan leiden tot een gerafelde rand van de print."

msgctxt "material_no_load_move_factor description"
msgid "A factor indicating how much the filament gets compressed between the feeder and the nozzle chamber, used to determine how far to move the material for a filament switch."
msgstr "Een factor die aangeeft hoeveel het filament wordt samengedrukt tussen de feeder en de nozzlekamer, om te bepalen hoe ver het materiaal moet worden verplaatst voor het verwisselen van filament."

msgctxt "roofing_angles description"
msgid "A list of integer line directions to use when the top surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Een lijst met gehele getallen voor lijnrichtingen die moet worden gebruikt wanneer voor de bovenste skinlagen een lijn- of zigzagpatroon wordt gebruikt. Elementen uit de lijst worden tijdens het printen van de lagen opeenvolgend gebruikt. Wanneer het einde van de lijst is bereikt, wordt deze weer van voren af aan gestart. De lijstitems zijn gescheiden door komma's en de hele lijst is binnen vierkante haken geplaatst. Standaard wordt een lege lijst gebruikt, wat inhoudt dat de traditionele standaardhoeken (45 en 135 graden) worden gebruikt."

msgctxt "skin_angles description"
msgid "A list of integer line directions to use when the top/bottom layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Een lijst met gehele getallen voor lijnrichtingen die moet worden gebruikt wanneer voor de boven-/onderlagen een lijn- of zigzagpatroon wordt gebruikt. Elementen uit de lijst worden tijdens het printen van de lagen opeenvolgend gebruikt. Wanneer het einde van de lijst bereikt is, wordt deze weer van voren af aan gestart. De lijstitems zijn gescheiden door komma's en de hele lijst is binnen vierkante haken geplaatst. Standaard wordt een lege lijst gebruikt, wat inhoudt dat de traditionele standaardhoeken (45 en 135 graden) worden gebruikt."

msgctxt "support_infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angle 0 degrees."
msgstr "Een lijst met gehele getallen voor lijnrichtingen die moet worden gebruikt. Elementen uit de lijst worden tijdens het printen van de lagen opeenvolgend gebruikt. Wanneer het einde van de lijst bereikt is, wordt deze weer van voren af aan gestart. De lijstitems zijn gescheiden door komma's en de hele lijst is binnen vierkante haken geplaatst. Standaard wordt een lege lijst gebruikt, wat inhoudt dat de standaardhoek van 0 graden wordt gebruikt."

msgctxt "support_bottom_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Een lijst met gehele getallen voor lijnrichtingen die moet worden gebruikt. Elementen uit de lijst worden tijdens het printen van de lagen opeenvolgend gebruikt. Wanneer het einde van de lijst bereikt is, wordt deze weer van voren af aan gestart. De lijstitems zijn gescheiden door komma's en de hele lijst is binnen vierkante haken geplaatst. Standaard wordt een lege lijst gebruikt, wat inhoudt dat de standaardhoeken (variërend tussen 45 en 135 graden als interfaces vrij dik of 90 graden zijn) worden gebruikt."

msgctxt "support_interface_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Een lijst met gehele getallen voor lijnrichtingen die moet worden gebruikt. Elementen uit de lijst worden tijdens het printen van de lagen opeenvolgend gebruikt. Wanneer het einde van de lijst bereikt is, wordt deze weer van voren af aan gestart. De lijstitems zijn gescheiden door komma's en de hele lijst is binnen vierkante haken geplaatst. Standaard wordt een lege lijst gebruikt, wat inhoudt dat de standaardhoeken (variërend tussen 45 en 135 graden als interfaces vrij dik of 90 graden zijn) worden gebruikt."

msgctxt "support_roof_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Een lijst met gehele getallen voor lijnrichtingen die moet worden gebruikt. Elementen uit de lijst worden tijdens het printen van de lagen opeenvolgend gebruikt. Wanneer het einde van de lijst bereikt is, wordt deze weer van voren af aan gestart. De lijstitems zijn gescheiden door komma's en de hele lijst is binnen vierkante haken geplaatst. Standaard wordt een lege lijst gebruikt, wat inhoudt dat de standaardhoeken (variërend tussen 45 en 135 graden als interfaces vrij dik of 90 graden zijn) worden gebruikt."

msgctxt "infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees for the lines and zig zag patterns and 45 degrees for all other patterns)."
msgstr "Een lijst met gehele getallen voor lijnrichtingen die moet worden gebruikt. Elementen uit de lijst worden tijdens het printen van de lagen opeenvolgend gebruikt. Wanneer het einde van de lijst bereikt is, wordt deze weer van voren af aan gestart. De lijstitems zijn gescheiden door komma's en de hele lijst is binnen vierkante haken geplaatst. Standaard wordt een lege lijst gebruikt, wat inhoudt dat de traditionele standaardhoeken (45 en 135 graden voor het lijn- en zigzagpatroon en 45 voor alle andere patronen) worden gebruikt."

msgctxt "nozzle_disallowed_areas description"
msgid "A list of polygons with areas the nozzle is not allowed to enter."
msgstr "Een lijst polygonen met gebieden waarin de nozzle niet mag komen."

msgctxt "machine_disallowed_areas description"
msgid "A list of polygons with areas the print head is not allowed to enter."
msgstr "Een lijst polygonen met gebieden waarin de printkop niet mag komen."

msgctxt "support_tree_branch_reach_limit description"
msgid "A recomendation to how far branches can move from the points they support. Branches can violate this value to reach their destination (buildplate or a flat part of the model). Lowering this value will make the support more sturdy, but increase the amount of branches (and because of that material usage/print time) "
msgstr "Een aanbeveling over hoe ver takken kunnen bewegen van de punten die ze ondersteunen. Takken kunnen deze waarde overschrijden om hun bestemming te bereiken (bouwplaat of een plat deel van het model). Als u deze waarde verlaagt, wordt de ondersteuning steviger, maar neemt het aantal takken toe (en daardoor materiaalgebruik/printtijd)."

msgctxt "extruder_prime_pos_abs label"
msgid "Absolute Extruder Prime Position"
msgstr "Absolute Positie voor Primen Extruder"

msgctxt "adaptive_layer_height_variation label"
msgid "Adaptive Layers Maximum Variation"
msgstr "Maximale variatie adaptieve lagen"

msgctxt "adaptive_layer_height_threshold label"
msgid "Adaptive Layers Topography Size"
msgstr "Topografieformaat aanpasbare lagen"

msgctxt "adaptive_layer_height_variation_step label"
msgid "Adaptive Layers Variation Step Size"
msgstr "Stapgrootte variatie adaptieve lagen"

msgctxt "adaptive_layer_height_enabled description"
msgid "Adaptive layers computes the layer heights depending on the shape of the model."
msgstr "Met adaptieve lagen berekent u de laaghoogte afhankelijk van de vorm van het model."

msgctxt "infill_wall_line_count description"
msgid "Add extra walls around the infill area. Such walls can make top/bottom skin lines sag down less which means you need less top/bottom skin layers for the same quality at the cost of some extra material.\nThis feature can combine with the Connect Infill Polygons to connect all the infill into a single extrusion path without the need for travels or retractions if configured right."
msgstr "Voeg extra wanden toe rondom de vulling. Deze wanden kunnen ervoor zorgen dat de skin aan de boven-/onderkant minder doorzakt. Dit betekent dat u met alleen wat extra materiaal voor dezelfde kwaliteit minder skinlagen aan de boven-/onderkant nodig hebt."
"Deze optie kan in combinatie met de optie 'Polygonen voor de vulling verbinden' worden gebruikt om alle vulling in één doorvoerpad te verbinden zonder extra bewegingen of intrekkingen, mits correct ingesteld."

msgctxt "platform_adhesion description"
msgid "Adhesion"
msgstr "Hechting"

msgctxt "material_adhesion_tendency label"
msgid "Adhesion Tendency"
msgstr "Hechtingsgevoeligheid"

msgctxt "skin_overlap description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines, as a percentage of the line widths of the skin lines and the innermost wall. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any percentage over 50% may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "Pas de mate van overlap tussen de wanden en (de eindpunten van) de skin-middellijnen aan, als percentage van de lijnbreedtes van de skin-lijnen en de binnenste wand. Met een lichte overlap kunnen de wanden goed hechten aan de skin. Houd er rekening mee dat met een gelijke lijnbreedte voor skin en wand, skin buiten de wand kan treden bij een percentage hoger dan 50%, omdat de nozzle van de skin-extruder op deze positie al voorbij het midden van de wand kan zijn."

msgctxt "skin_overlap_mm description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any value over half the width of the wall may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "Pas de mate van overlap tussen de wanden en (de eindpunten van) de skin-middellijnen aan. Met een lichte overlap kunnen de wanden goed hechten aan de skin. Houd er rekening mee dat met een gelijke lijnbreedte voor skin en wand, skin buiten de wand kan treden bij een waarde groter dan de halve wandbreedte, omdat de nozzle van de skin-extruder op deze positie het midden van de wand al kan hebben bereikt."

msgctxt "infill_sparse_density description"
msgid "Adjusts the density of infill of the print."
msgstr "Past de vuldichtheid van de print aan."

msgctxt "support_interface_density description"
msgid "Adjusts the density of the roofs and floors of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Past de dichtheid van de daken en vloeren van de supportstructuur aan. Met een hogere waarde krijgt u een betere overhang, maar is de supportstructuur moeilijker te verwijderen."

msgctxt "support_tree_top_rate description"
msgid "Adjusts the density of the support structure used to generate the tips of the branches. A higher value results in better overhangs, but the supports are harder to remove. Use Support Roof for very high values or ensure support density is similarly high at the top."
msgstr "Hiermee past u de dichtheid aan van de ondersteunende structuur die wordt gebruikt om de tips van de takken te genereren. Een hogere waarde resulteert in een betere overhang, maar de ondersteuning is moeilijker te verwijderen. Gebruik ondersteunend dak voor zeer hoge waarden of zorg ervoor dat de ondersteuningsdichtheid aan de bovenkant even hoog is."

msgctxt "support_infill_rate description"
msgid "Adjusts the density of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Past de dichtheid van de supportstructuur aan. Met een hogere waarde krijgt u een betere overhang, maar is de supportstructuur moeilijker te verwijderen."

msgctxt "material_diameter description"
msgid "Adjusts the diameter of the filament used. Match this value with the diameter of the used filament."
msgstr "Bepaalt de diameter van het gebruikte filament. Pas deze waarde aan de diameter van het gebruikte filament aan."

msgctxt "support_type description"
msgid "Adjusts the placement of the support structures. The placement can be set to touching build plate or everywhere. When set to everywhere the support structures will also be printed on the model."
msgstr "Past de plaatsing van de supportstructuur aan. De plaatsing kan worden ingesteld op Platform aanraken of Overal. Wanneer deze optie ingesteld is op Overal, worden de supportstructuren ook op het model geprint."

msgctxt "prime_tower_wipe_enabled description"
msgid "After printing the prime tower with one nozzle, wipe the oozed material from the other nozzle off on the prime tower."
msgstr "Veeg na het printen van de primepijler met één nozzle het doorgevoerde materiaal van de andere nozzle af aan de primepijler."

msgctxt "retraction_hop_after_extruder_switch description"
msgid "After the machine switched from one extruder to the other, the build plate is lowered to create clearance between the nozzle and the print. This prevents the nozzle from leaving oozed material on the outside of a print."
msgstr "Nadat de machine van de ene extruder naar de andere is gewisseld, wordt het platform omlaag gebracht om ruimte te creëren tussen de nozzle en de print. Hiermee wordt voorkomen dat de nozzle doorgevoerd materiaal achterlaat op de buitenzijde van een print."

msgctxt "retraction_combing option all"
msgid "All"
msgstr "Alles"

msgctxt "print_sequence option all_at_once"
msgid "All at Once"
msgstr "Alles Tegelijk"

msgctxt "resolution description"
msgid "All settings that influence the resolution of the print. These settings have a large impact on the quality (and print time)"
msgstr "Alle instellingen die invloed hebben op de resolutie van de print. Deze instellingen hebben een grote invloed op de kwaliteit (en printtijd)."

msgctxt "user_defined_print_order_enabled description"
msgid "Allows you to order the object list to manually set the print sequence. First object from the list will be printed first."
msgstr "Hiermee kunt u de objectlijst ordenen om de printvolgorde handmatig in te stellen. Het eerste object in de lijst wordt als eerste geprint."

msgctxt "alternate_extra_perimeter label"
msgid "Alternate Extra Wall"
msgstr "Afwisselend Extra Wand"

msgctxt "alternate_carve_order label"
msgid "Alternate Mesh Removal"
msgstr "Verwijderen van afwisselend raster"

msgctxt "material_alternate_walls label"
msgid "Alternate Wall Directions"
msgstr "Alternerende wandrichtingen"

msgctxt "material_alternate_walls description"
msgid "Alternate wall directions every other layer and inset. Useful for materials that can build up stress, like for metal printing."
msgstr "Alternerende wandrichtingen na elke laag en instroming. Nuttig voor materialen die spanning op kunnen bouwen, bijvoorbeeld voor het printen van metaal."

msgctxt "machine_buildplate_type option aluminum"
msgid "Aluminum"
msgstr "Aluminium"

msgctxt "machine_always_write_active_tool label"
msgid "Always Write Active Tool"
msgstr "Tool voor altijd actief schrijven"

msgctxt "travel_retract_before_outer_wall description"
msgid "Always retract when moving to start an outer wall."
msgstr "Altijd intrekken voordat wordt bewogen om met een buitenwand te beginnen."

msgctxt "xy_offset description"
msgid "Amount of offset applied to all polygons in each layer. Positive values can compensate for too big holes; negative values can compensate for too small holes."
msgstr "De mate van offset die wordt toegepast op alle polygonen in elke laag. Met positieve waarden compenseert u te grote gaten, met negatieve waarden compenseert u te kleine gaten."

msgctxt "xy_offset_layer_0 description"
msgid "Amount of offset applied to all polygons in the first layer. A negative value can compensate for squishing of the first layer known as \"elephant's foot\"."
msgstr "De mate van offset die wordt toegepast op alle polygonen in de eerste laag. Met negatieve waarden compenseert u het samenpersen van de eerste laag, ook wel 'olifantenpoot' genoemd."

msgctxt "support_offset description"
msgid "Amount of offset applied to all support polygons in each layer. Positive values can smooth out the support areas and result in more sturdy support."
msgstr "De mate van offset die wordt toegepast op alle steunpolygonen in elke laag. Met positieve waarden kunt u de draagvlakken effenen en krijgt u een stevigere supportstructuur."

msgctxt "support_bottom_offset description"
msgid "Amount of offset applied to the floors of the support."
msgstr "De mate van offset die wordt toegepast op de supportvloeren."

msgctxt "support_roof_offset description"
msgid "Amount of offset applied to the roofs of the support."
msgstr "De mate van offset die wordt toegepast op de supportdaken."

msgctxt "support_interface_offset description"
msgid "Amount of offset applied to the support interface polygons."
msgstr "De mate van offset die wordt toegepast op de verbindingspolygonen."

msgctxt "wipe_retraction_amount description"
msgid "Amount to retract the filament so it does not ooze during the wipe sequence."
msgstr "Volume filament dat moet worden ingetrokken om te voorkomen dat filament verloren gaat tijdens het afvegen."

msgctxt "sub_div_rad_add description"
msgid "An addition to the radius from the center of each cube to check for the boundary of the model, as to decide whether this cube should be subdivided. Larger values lead to a thicker shell of small cubes near the boundary of the model."
msgstr "Een aanvulling op de straal vanuit het midden van elk blok om de rand van het model te detecteren, om te bepalen of het blok moet worden onderverdeeld. Een hogere waarde leidt tot een dikkere shell voor kleine blokken bij de rand van het model."

msgctxt "anti_overhang_mesh label"
msgid "Anti Overhang Mesh"
msgstr "Raster tegen overhang"

msgctxt "material_anti_ooze_retracted_position label"
msgid "Anti-ooze Retracted Position"
msgstr "Intrekpositie voor niet-uitlopen"

msgctxt "material_anti_ooze_retraction_speed label"
msgid "Anti-ooze Retraction Speed"
msgstr "Intreksnelheid voor niet-uitlopen"

msgctxt "machine_use_extruder_offset_to_offset_coords description"
msgid "Apply the extruder offset to the coordinate system. Affects all extruders."
msgstr "Pas de extruderoffset toe op het coördinatensysteem. Van toepassing op alle extruders."

msgctxt "interlocking_enable description"
msgid "At the locations where models touch, generate an interlocking beam structure. This improves the adhesion between models, especially models printed in different materials."
msgstr "Genereer op de plaatsen waar modellen elkaar raken een in elkaar grijpende balkstructuur. Dit verbetert de hechting tussen modellen, vooral modellen die in verschillende materialen zijn geprint."

msgctxt "travel_avoid_other_parts label"
msgid "Avoid Printed Parts When Traveling"
msgstr "Geprinte delen mijden tijdens bewegingen"

msgctxt "travel_avoid_supports label"
msgid "Avoid Supports When Traveling"
msgstr "Supportstructuren mijden tijdens bewegingen"

msgctxt "z_seam_position option back"
msgid "Back"
msgstr "Achter"

msgctxt "z_seam_position option backleft"
msgid "Back Left"
msgstr "Linksachter"

msgctxt "z_seam_position option backright"
msgid "Back Right"
msgstr "Rechtsachter"

msgctxt "machine_gcode_flavor option BFB"
msgid "Bits from Bytes"
msgstr "Bits from Bytes"

msgctxt "magic_mesh_surface_mode option both"
msgid "Both"
msgstr "Beide"

msgctxt "support_interface_priority option nothing"
msgid "Both overlap"
msgstr "Beide overlappen"

msgctxt "bottom_layers label"
msgid "Bottom Layers"
msgstr "Bodemlagen"

msgctxt "top_bottom_pattern_0 label"
msgid "Bottom Pattern Initial Layer"
msgstr "Eerste laag patroon onderkant"

msgctxt "bottom_skin_expand_distance label"
msgid "Bottom Skin Expand Distance"
msgstr "Uitbreidingsafstand van onderste skinlaag"

msgctxt "bottom_skin_preshrink label"
msgid "Bottom Skin Removal Width"
msgstr "Verwijderingsbreedte onderste skinlaag"

msgctxt "bottom_thickness label"
msgid "Bottom Thickness"
msgstr "Bodemdikte"

msgctxt "support_tree_top_rate label"
msgid "Branch Density"
msgstr "Takdichtheid"

msgctxt "support_tree_branch_diameter label"
msgid "Branch Diameter"
msgstr "Takdiameter"

msgctxt "support_tree_branch_diameter_angle label"
msgid "Branch Diameter Angle"
msgstr "Hoek takdiameter"

msgctxt "material_break_preparation_retracted_position label"
msgid "Break Preparation Retracted Position"
msgstr "Intrekpositie voor voorbereiding van afbreken"

msgctxt "material_break_preparation_speed label"
msgid "Break Preparation Retraction Speed"
msgstr "Intreksnelheid voor voorbereiding van afbreken"

msgctxt "material_break_preparation_temperature label"
msgid "Break Preparation Temperature"
msgstr "Temperatuur voor voorbereiding van afbreken"

msgctxt "material_break_retracted_position label"
msgid "Break Retracted Position"
msgstr "Intrekpositie voor afbreken"

msgctxt "material_break_speed label"
msgid "Break Retraction Speed"
msgstr "Intreksnelheid voor afbreken"

msgctxt "material_break_temperature label"
msgid "Break Temperature"
msgstr "Temperatuur voor afbreken"

msgctxt "support_skip_some_zags label"
msgid "Break Up Support In Chunks"
msgstr "Supportstructuur in Stukken Breken"

msgctxt "bridge_fan_speed label"
msgid "Bridge Fan Speed"
msgstr "Ventilatorsnelheid brug"

msgctxt "bridge_enable_more_layers label"
msgid "Bridge Has Multiple Layers"
msgstr "Brug heeft meerdere lagen"

msgctxt "bridge_skin_density_2 label"
msgid "Bridge Second Skin Density"
msgstr "Dichtheid tweede brugskin"

msgctxt "bridge_fan_speed_2 label"
msgid "Bridge Second Skin Fan Speed"
msgstr "Ventilatorsnelheid tweede brugskin"

msgctxt "bridge_skin_material_flow_2 label"
msgid "Bridge Second Skin Flow"
msgstr "Doorvoer tweede brugskin"

msgctxt "bridge_skin_speed_2 label"
msgid "Bridge Second Skin Speed"
msgstr "Snelheid tweede brugskin"

msgctxt "bridge_skin_density label"
msgid "Bridge Skin Density"
msgstr "Dichtheid brugskin"

msgctxt "bridge_skin_material_flow label"
msgid "Bridge Skin Flow"
msgstr "Doorvoer brugskin"

msgctxt "bridge_skin_speed label"
msgid "Bridge Skin Speed"
msgstr "Snelheid brugskin"

msgctxt "bridge_skin_support_threshold label"
msgid "Bridge Skin Support Threshold"
msgstr "Drempelwaarde voor brugskinsupport"

msgctxt "bridge_sparse_infill_max_density label"
msgid "Bridge Sparse Infill Max Density"
msgstr "Maximale dichtheid van dunne vulling brugskin"

msgctxt "bridge_skin_density_3 label"
msgid "Bridge Third Skin Density"
msgstr "Dichtheid derde brugskin"

msgctxt "bridge_fan_speed_3 label"
msgid "Bridge Third Skin Fan Speed"
msgstr "Ventilatorsnelheid derde brugskin"

msgctxt "bridge_skin_material_flow_3 label"
msgid "Bridge Third Skin Flow"
msgstr "Doorvoer derde brugskin"

msgctxt "bridge_skin_speed_3 label"
msgid "Bridge Third Skin Speed"
msgstr "Snelheid derde brugskin"

msgctxt "bridge_wall_coast label"
msgid "Bridge Wall Coasting"
msgstr "Coasting brugwand"

msgctxt "bridge_wall_material_flow label"
msgid "Bridge Wall Flow"
msgstr "Doorvoer brugwand"

msgctxt "bridge_wall_speed label"
msgid "Bridge Wall Speed"
msgstr "Snelheid brugwand"

msgctxt "adhesion_type option brim"
msgid "Brim"
msgstr "Brim"

msgctxt "brim_inside_margin label"
msgid "Brim Avoid Margin"
msgstr "Marge voor vermijden van rand"

msgctxt "brim_gap label"
msgid "Brim Distance"
msgstr "Brimafstand"

msgctxt "brim_line_count label"
msgid "Brim Line Count"
msgstr "Aantal Brimlijnen"

msgctxt "brim_location label"
msgid "Brim Location"
msgstr "Locatie rand"

msgctxt "brim_replaces_support label"
msgid "Brim Replaces Support"
msgstr "Brim vervangt supportstructuur"

msgctxt "brim_width label"
msgid "Brim Width"
msgstr "Breedte Brim"

msgctxt "platform_adhesion label"
msgid "Build Plate Adhesion"
msgstr "Hechting aan Platform"

msgctxt "adhesion_extruder_nr label"
msgid "Build Plate Adhesion Extruder"
msgstr "Extruder Hechting aan Platform"

msgctxt "adhesion_type label"
msgid "Build Plate Adhesion Type"
msgstr "Type Hechting aan Platform"

msgctxt "machine_buildplate_type label"
msgid "Build Plate Material"
msgstr "Materiaal van het platform"

msgctxt "machine_shape label"
msgid "Build Plate Shape"
msgstr "Vorm van het platform"

msgctxt "material_bed_temperature label"
msgid "Build Plate Temperature"
msgstr "Platformtemperatuur"

msgctxt "material_bed_temperature_layer_0 label"
msgid "Build Plate Temperature Initial Layer"
msgstr "Platformtemperatuur voor de eerste laag"

msgctxt "build_volume_temperature label"
msgid "Build Volume Temperature"
msgstr "Temperatuur werkvolume"

msgctxt "bv_temp_anomaly_limit label"
msgid "Build Volume temperature Limit"
msgstr "Limiet temperatuur bouwvolume"

msgctxt "bv_temp_warn_limit label"
msgid "Build Volume temperature Warning"
msgstr "Waarschuwing temperatuur bouwvolume"

msgctxt "prime_tower_brim_enable description"
msgid "By enabling this setting, your prime-tower will get a brim, even if the model doesn't. If you want a sturdier base for a high tower, you can increase the base height."
msgstr "Door deze instelling in te schakelen, krijgt uw prime toren een brim, zelfs als het model dat niet heeft. Als u een stevigere basis wilt voor een hoge toren, kunt u de basis hoogte verhogen."

msgctxt "center_object label"
msgid "Center Object"
msgstr "Object centreren"

msgctxt "conical_overhang_enabled description"
msgid "Change the geometry of the printed model such that minimal support is required. Steep overhangs will become shallow overhangs. Overhanging areas will drop down to become more vertical."
msgstr "Verander de geometrie van het geprinte model dusdanig dat minimale support is vereist. Een steile overhang wordt een vlakke overhang. Overhangende gedeelten worden verlaagd zodat deze meer verticaal komen te staan."

msgctxt "support_structure description"
msgid "Chooses between the techniques available to generate support. \"Normal\" support creates a support structure directly below the overhanging parts and drops those areas straight down. \"Tree\" support creates branches towards the overhanging areas that support the model on the tips of those branches, and allows the branches to crawl around the model to support it from the build plate as much as possible."
msgstr "Kiest tussen de beschikbare technieken om support te genereren. \"Normale\" support creëert een supportstructuur direct onder de overhangende delen en laat die gebieden recht naar beneden vallen. \"Boom\"-support creëert takken naar de overhangende gebieden die het model op de toppen van die takken ondersteunen, en laat de takken rond het model kruipen om het zoveel mogelijk vanaf het platform te ondersteunen."

msgctxt "coasting_speed label"
msgid "Coasting Speed"
msgstr "Coasting-snelheid"

msgctxt "coasting_volume label"
msgid "Coasting Volume"
msgstr "Coasting-volume"

msgctxt "coasting_enable description"
msgid "Coasting replaces the last part of an extrusion path with a travel path. The oozed material is used to print the last piece of the extrusion path in order to reduce stringing."
msgstr "Met coasting wordt het laatste gedeelte van een doorvoerpad vervangen door een beweging. Het doorgevoerde materiaal wordt gebruikt om het laatste gedeelte van het doorvoerpad te printen, om draadvorming te verminderen."

msgctxt "retraction_combing label"
msgid "Combing Mode"
msgstr "Combing-modus"

msgctxt "retraction_combing description"
msgid "Combing keeps the nozzle within already printed areas when traveling. This results in slightly longer travel moves but reduces the need for retractions. If combing is off, the material will retract and the nozzle moves in a straight line to the next point. It is also possible to avoid combing over top/bottom skin areas or to only comb within the infill."
msgstr "Met combing blijft de nozzle tijdens bewegingen binnen eerder geprinte delen. Hierdoor zijn de bewegingen iets langer, maar hoeft het filament minder vaak te worden ingetrokken. Als combing is uitgeschakeld, wordt het materiaal ingetrokken en beweegt de nozzle in een rechte lijn naar het volgende punt. Het is ook mogelijk om combing over boven-/onderskingedeelten te voorkomen of combing alleen binnen de vulling te gebruiken."

msgctxt "command_line_settings label"
msgid "Command Line Settings"
msgstr "Instellingen opdrachtregel"

msgctxt "infill_pattern option concentric"
msgid "Concentric"
msgstr "Concentrisch"

msgctxt "ironing_pattern option concentric"
msgid "Concentric"
msgstr "Concentrisch"

msgctxt "roofing_pattern option concentric"
msgid "Concentric"
msgstr "Concentrisch"

msgctxt "support_bottom_pattern option concentric"
msgid "Concentric"
msgstr "Concentrisch"

msgctxt "support_interface_pattern option concentric"
msgid "Concentric"
msgstr "Concentrisch"

msgctxt "support_pattern option concentric"
msgid "Concentric"
msgstr "Concentrisch"

msgctxt "support_roof_pattern option concentric"
msgid "Concentric"
msgstr "Concentrisch"

msgctxt "top_bottom_pattern option concentric"
msgid "Concentric"
msgstr "Concentrisch"

msgctxt "top_bottom_pattern_0 option concentric"
msgid "Concentric"
msgstr "Concentrisch"

msgctxt "support_conical_angle label"
msgid "Conical Support Angle"
msgstr "Hoek Conische Supportstructuur"

msgctxt "support_conical_min_width label"
msgid "Conical Support Minimum Width"
msgstr "Minimale Breedte Conische Supportstructuur"

msgctxt "zig_zaggify_infill label"
msgid "Connect Infill Lines"
msgstr "Vullijnen verbinden"

msgctxt "connect_infill_polygons label"
msgid "Connect Infill Polygons"
msgstr "Vulpolygonen Verbinden"

msgctxt "zig_zaggify_support label"
msgid "Connect Support Lines"
msgstr "Supportstructuurlijnen verbinden"

msgctxt "support_connect_zigzags label"
msgid "Connect Support ZigZags"
msgstr "Zigzaglijnen Supportstructuur Verbinden"

msgctxt "connect_skin_polygons label"
msgid "Connect Top/Bottom Polygons"
msgstr "Boven-/onderkant Polygonen Verbinden"

msgctxt "connect_infill_polygons description"
msgid "Connect infill paths where they run next to each other. For infill patterns which consist of several closed polygons, enabling this setting greatly reduces the travel time."
msgstr "Vulpaden verbinden waar ze naast elkaar lopen. Bij vulpatronen die uit meerdere gesloten polygonen bestaan, wordt met deze instelling de bewegingstijd aanzienlijk verkort."

msgctxt "support_connect_zigzags description"
msgid "Connect the ZigZags. This will increase the strength of the zig zag support structure."
msgstr "Verbind de zigzaglijnen. Hiermee versterkt u de zigzag-supportstructuur."

msgctxt "zig_zaggify_support description"
msgid "Connect the ends of the support lines together. Enabling this setting can make your support more sturdy and reduce underextrusion, but it will cost more material."
msgstr "Verbind de uiteinden van de supportstructuurlijnen met elkaar. Als u deze instelling inschakelt, maakt u de supportstructuur robuuster en vermindert u onderextrusie. Er wordt echter meer materiaal verbruikt."

msgctxt "zig_zaggify_infill description"
msgid "Connect the ends where the infill pattern meets the inner wall using a line which follows the shape of the inner wall. Enabling this setting can make the infill adhere to the walls better and reduce the effects of infill on the quality of vertical surfaces. Disabling this setting reduces the amount of material used."
msgstr "Verbindt de uiteinden waar het vulpatroon bij de binnenwand komt, met een lijn die de vorm van de binnenwand volgt. Als u deze instelling inschakelt, kan de vulling beter hechten aan de wanden en wordt de invloed van de vulling op de kwaliteit van de verticale oppervlakken kleiner. Als u deze instelling uitschakelt, wordt er minder materiaal gebruikt."

msgctxt "connect_skin_polygons description"
msgid "Connect top/bottom skin paths where they run next to each other. For the concentric pattern enabling this setting greatly reduces the travel time, but because the connections can happen midway over infill this feature can reduce the top surface quality."
msgstr "Verbind skinpaden aan de boven-/onderkant waar ze naast elkaar lopen. Met deze instelling wordt bij concentrische patronen de bewegingstijd aanzienlijk verkort. Dit kan echter ten koste gaan van de kwaliteit van de bovenste laag aangezien de verbindingen in het midden van de vulling kunnen komen te liggen."

msgctxt "z_seam_corner description"
msgid "Control whether corners on the model outline influence the position of the seam. None means that corners have no influence on the seam position. Hide Seam makes the seam more likely to occur on an inside corner. Expose Seam makes the seam more likely to occur on an outside corner. Hide or Expose Seam makes the seam more likely to occur at an inside or outside corner. Smart Hiding allows both inside and outside corners, but chooses inside corners more frequently, if appropriate."
msgstr "Instellen of hoeken in het model invloed hebben op de positie van de naad. Geen wil zeggen dat hoeken geen invloed hebben op de positie van de naad. Met Naad Verbergen is de kans groter dat de naad op een binnenhoek komt. Met Naad Zichtbaar Maken is de kans groter dat de naad op een buitenhoek komt. Met Naad Verbergen of Naad Zichtbaar Maken is de kans groter dat de naad op een binnen- of buitenhoek komt. Met Slim Verbergen zijn zowel binnen- als buitenhoeken mogelijk, maar wordt er vaker (indien van toepassing) gebruikgemaakt van binnenhoeken."

msgctxt "infill_multiplier description"
msgid "Convert each infill line to this many lines. The extra lines do not cross over each other, but avoid each other. This makes the infill stiffer, but increases print time and material usage."
msgstr "Zet elke vullijn om naar zoveel keer vullijnen. De extra lijnen kruisen elkaar niet, maar mijden elkaar. Hierdoor wordt de vulling stijver, maar duurt het printen langer en wordt er meer materiaal verbruikt."

msgctxt "machine_nozzle_cool_down_speed label"
msgid "Cool Down Speed"
msgstr "Afkoelsnelheid"

msgctxt "cooling description"
msgid "Cooling"
msgstr "Koelen"

msgctxt "cooling label"
msgid "Cooling"
msgstr "Koelen"

msgctxt "infill_pattern option cross"
msgid "Cross"
msgstr "Kruis"

msgctxt "support_pattern option cross"
msgid "Cross"
msgstr "Kruis"

msgctxt "infill_pattern option cross_3d"
msgid "Cross 3D"
msgstr "Kruis 3D"

msgctxt "cross_infill_pocket_size label"
msgid "Cross 3D Pocket Size"
msgstr "Luchtbelgrootte bij Kruis 3D"

msgctxt "cross_support_density_image label"
msgid "Cross Fill Density Image for Support"
msgstr "Dichtheid kruisvulling afbeelding voor supportstructuur"

msgctxt "cross_infill_density_image label"
msgid "Cross Infill Density Image"
msgstr "Dichtheid kruisvulling afbeelding"

msgctxt "material_crystallinity label"
msgid "Crystalline Material"
msgstr "Kristallijnmateriaal"

msgctxt "infill_pattern option cubic"
msgid "Cubic"
msgstr "Kubisch"

msgctxt "infill_pattern option cubicsubdiv"
msgid "Cubic Subdivision"
msgstr "Kubische onderverdeling"

msgctxt "sub_div_rad_add label"
msgid "Cubic Subdivision Shell"
msgstr "Kubische onderverdeling shell"

msgctxt "cutting_mesh label"
msgid "Cutting Mesh"
msgstr "Snijdend raster"

msgctxt "material_flow_temp_graph description"
msgid "Data linking material flow (in mm3 per second) to temperature (degrees Celsius)."
msgstr "Grafiek om de materiaaldoorvoer (in mm3 per seconde) te koppelen aan de temperatuur (graden Celsius)."

msgctxt "machine_acceleration label"
msgid "Default Acceleration"
msgstr "Standaardacceleratie"

msgctxt "default_material_bed_temperature label"
msgid "Default Build Plate Temperature"
msgstr "Standaardtemperatuur platform"

msgctxt "machine_max_jerk_e label"
msgid "Default Filament Jerk"
msgstr "Standaard Filamentschok"

msgctxt "default_material_print_temperature label"
msgid "Default Printing Temperature"
msgstr "Standaard printtemperatuur"

msgctxt "machine_max_jerk_xy label"
msgid "Default X-Y Jerk"
msgstr "Standaard X-/Y-schok"

msgctxt "machine_max_jerk_z label"
msgid "Default Z Jerk"
msgstr "Standaard Z-schok"

msgctxt "machine_max_jerk_xy description"
msgid "Default jerk for movement in the horizontal plane."
msgstr "De standaardschok voor beweging in het horizontale vlak."

msgctxt "machine_max_jerk_z description"
msgid "Default jerk for the motor of the Z-direction."
msgstr "De standaardschok voor de motor in de Z-richting."

msgctxt "machine_max_jerk_e description"
msgid "Default jerk for the motor of the filament."
msgstr "De standaardschok voor de motor voor het filament."

msgctxt "bridge_settings_enabled description"
msgid "Detect bridges and modify print speed, flow and fan settings while bridges are printed."
msgstr "Hiermee detecteert u bruggen en past u de instellingen voor de printsnelheid, doorvoer en ventilator aan tijdens het printen van bruggen."

msgctxt "inset_direction description"
msgid "Determines the order in which walls are printed. Printing outer walls earlier helps with dimensional accuracy, as faults from inner walls cannot propagate to the outside. However printing them later allows them to stack better when overhangs are printed. When there is an uneven amount of total innner walls, the 'center last line' is always printed last."
msgstr "Bepaalt de volgorde waarin de wanden worden geprint. Wanneer u de buitenwanden het eerst print, bevordert u de nauwkeurigheid van de afmetingen, omdat fouten in de binnenwanden niet worden overgedragen op de buitenzijde. Door ze later te printen kunt u echter beter stapelen wanneer de overhangs worden geprint. Bij een oneven aantal binnenwanden wordt de 'middelste laatste lijn' altijd als laatste afgedrukt."

msgctxt "infill_mesh_order description"
msgid "Determines the priority of this mesh when considering multiple overlapping infill meshes. Areas where multiple infill meshes overlap will take on the settings of the mesh with the highest rank. An infill mesh with a higher rank will modify the infill of infill meshes with lower rank and normal meshes."
msgstr "Bepaalt de prioriteit van dit raster bij meerdere overlappende vulrasters. Gebieden met meerdere overlappende vulrasters krijgen de instellingen van het vulraster met de hoogste rang. Bij een vulraster met een hogere rang wordt de vulling van vulrasters met een lagere rang en normale rasters aangepast."

msgctxt "lightning_infill_support_angle description"
msgid "Determines when a lightning infill layer has to support anything above it. Measured in the angle given the thickness of a layer."
msgstr "Bepaalt wanneer een bliksemvullaag iets moet ondersteunen dat zich boven de vullaag bevindt. Gemeten in de hoek bepaald door de laagdikte."

msgctxt "lightning_infill_overhang_angle description"
msgid "Determines when a lightning infill layer has to support the model above it. Measured in the angle given the thickness."
msgstr "Bepaalt wanneer een bliksemvullaag het model boven de laag moet ondersteunen. Gemeten in de hoek bepaald door de laagdikte."

msgctxt "material_diameter label"
msgid "Diameter"
msgstr "Diameter"

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model label"
msgid "Diameter Increase To Model"
msgstr "Diameterverhoging naar model"

msgctxt "support_tree_bp_diameter description"
msgid "Diameter every branch tries to achieve when reaching the buildplate. Improves bed adhesion."
msgstr "Diameter elke tak probeert te bereiken bij het bereiken van de bouwplaat. Verbetert de hechting van het bed."

msgctxt "adhesion_type description"
msgid "Different options that help to improve both priming your extrusion and adhesion to the build plate. Brim adds a single layer flat area around the base of your model to prevent warping. Raft adds a thick grid with a roof below the model. Skirt is a line printed around the model, but not connected to the model."
msgstr "Er zijn verschillende opties die u helpen zowel de voorbereiding van de doorvoer als de hechting aan het platform te verbeteren. Met de optie Brim legt u in de eerste laag extra materiaal rondom de voet van het model om vervorming te voorkomen. Met de optie Raft legt u een dik raster met een dak onder het model. Met de optie Skirt print u rond het model een lijn die niet met het model is verbonden."

msgctxt "machine_disallowed_areas label"
msgid "Disallowed Areas"
msgstr "Verboden gebieden"

msgctxt "infill_line_distance description"
msgid "Distance between the printed infill lines. This setting is calculated by the infill density and the infill line width."
msgstr "De afstand tussen de geprinte vullijnen. Deze instelling wordt berekend op basis van de dichtheid van de vulling en de lijnbreedte van de vulling."

msgctxt "support_initial_layer_line_distance description"
msgid "Distance between the printed initial layer support structure lines. This setting is calculated by the support density."
msgstr "Afstand tussen de lijnen van de supportstructuur voor de eerste laag. Deze wordt berekend op basis van de dichtheid van de supportstructuur."

msgctxt "support_bottom_line_distance description"
msgid "Distance between the printed support floor lines. This setting is calculated by the Support Floor Density, but can be adjusted separately."
msgstr "De afstand tussen de geprinte lijnen van de supportvloer. Deze instelling wordt berekend op basis van de dichtheid van de supportvloer, maar kan onafhankelijk worden aangepast."

msgctxt "support_roof_line_distance description"
msgid "Distance between the printed support roof lines. This setting is calculated by the Support Roof Density, but can be adjusted separately."
msgstr "De afstand tussen de geprinte lijnen van het supportdak. Deze instelling wordt berekend op basis van de dichtheid van het supportdak, maar kan onafhankelijk worden aangepast."

msgctxt "support_line_distance description"
msgid "Distance between the printed support structure lines. This setting is calculated by the support density."
msgstr "De afstand tussen de geprinte lijnen van de supportstructuur. Deze instelling wordt berekend op basis van de dichtheid van de supportstructuur."

msgctxt "support_bottom_distance description"
msgid "Distance from the print to the bottom of the support. Note that this is rounded up to the next layer height."
msgstr "Afstand van de print tot de onderkant van de ondersteuning. Let op dat dit wordt afgerond naar de volgende laaghoogte."

msgctxt "support_top_distance description"
msgid "Distance from the top of the support to the print."
msgstr "De afstand van de bovenkant van de supportstructuur tot de print."

msgctxt "support_z_distance description"
msgid "Distance from the top/bottom of the support structure to the print. This gap provides clearance to remove the supports after the model is printed. The topmost support layer below the model might be a fraction of regular layers."
msgstr "Afstand van de boven-/onderkant van de ondersteuningsstructuur tot de print. Deze opening zorgt voor ruimte om de ondersteuningen te verwijderen nadat het model is geprint. De bovenste ondersteuningslaag onder het model kan een fractie zijn van de normale lagen."

msgctxt "infill_wipe_dist description"
msgid "Distance of a travel move inserted after every infill line, to make the infill stick to the walls better. This option is similar to infill overlap, but without extrusion and only on one end of the infill line."
msgstr "De afstand voor een beweging die na het printen van elke vullijn wordt ingevoegd, om ervoor te zorgen dat de vulling beter aan de wanden hecht. Deze optie lijkt op de optie voor overlap van vulling. Tijdens deze beweging is er echter geen doorvoer en de beweging vindt maar aan één uiteinde van de vullijn plaats."

msgctxt "wall_0_wipe_dist description"
msgid "Distance of a travel move inserted after the outer wall, to hide the Z seam better."
msgstr "Afstand van een beweging die ingevoegd is na de buitenwand, om de Z-naad beter te maskeren."

msgctxt "draft_shield_dist description"
msgid "Distance of the draft shield from the print, in the X/Y directions."
msgstr "De afstand tussen het tochtscherm en de print, in de X- en Y-richting."

msgctxt "ooze_shield_dist description"
msgid "Distance of the ooze shield from the print, in the X/Y directions."
msgstr "De afstand tussen het uitloopscherm en de print, in de X- en Y-richting."

msgctxt "support_xy_distance_overhang description"
msgid "Distance of the support structure from the overhang in the X/Y directions."
msgstr "Afstand tussen de supportstructuur en de overhang in de X- en Y-richting."

msgctxt "support_xy_distance description"
msgid "Distance of the support structure from the print in the X/Y directions."
msgstr "Afstand tussen de supportstructuur en de print, in de X- en Y-richting."

msgctxt "meshfix_fluid_motion_shift_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr "Afstandspunten worden verschoven om het pad vloeiend te maken"

msgctxt "meshfix_fluid_motion_small_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr "Afstandspunten worden verschoven om het pad vloeiend te maken"

msgctxt "min_infill_area description"
msgid "Don't generate areas of infill smaller than this (use skin instead)."
msgstr "Genereer geen gebieden met vulling die kleiner zijn dan deze waarde (gebruik in plaats daarvan een skin)."

msgctxt "draft_shield_height label"
msgid "Draft Shield Height"
msgstr "Hoogte Tochtscherm"

msgctxt "draft_shield_height_limitation label"
msgid "Draft Shield Limitation"
msgstr "Beperking Tochtscherm"

msgctxt "draft_shield_dist label"
msgid "Draft Shield X/Y Distance"
msgstr "Tochtscherm X-/Y-afstand"

msgctxt "support_mesh_drop_down label"
msgid "Drop Down Support Mesh"
msgstr "Supportraster verlagen"

msgctxt "dual label"
msgid "Dual Extrusion"
msgstr "Dubbele Doorvoer"

msgctxt "machine_shape option elliptic"
msgid "Elliptic"
msgstr "Ovaal"

msgctxt "acceleration_enabled label"
msgid "Enable Acceleration Control"
msgstr "Acceleratieregulering Inschakelen"

msgctxt "bridge_settings_enabled label"
msgid "Enable Bridge Settings"
msgstr "Bruginstellingen inschakelen"

msgctxt "coasting_enable label"
msgid "Enable Coasting"
msgstr "Coasting Inschakelen"

msgctxt "support_conical_enabled label"
msgid "Enable Conical Support"
msgstr "Conische supportstructuur inschakelen"

msgctxt "draft_shield_enabled label"
msgid "Enable Draft Shield"
msgstr "Tochtscherm Inschakelen"

msgctxt "meshfix_fluid_motion_enabled label"
msgid "Enable Fluid Motion"
msgstr "Vloeiende beweging inschakelen"

msgctxt "ironing_enabled label"
msgid "Enable Ironing"
msgstr "Strijken inschakelen"

msgctxt "jerk_enabled label"
msgid "Enable Jerk Control"
msgstr "Schokregulering Inschakelen"

msgctxt "machine_nozzle_temp_enabled label"
msgid "Enable Nozzle Temperature Control"
msgstr "Regulering van de nozzletemperatuur inschakelen"

msgctxt "ooze_shield_enabled label"
msgid "Enable Ooze Shield"
msgstr "Uitloopscherm Inschakelen"

msgctxt "prime_blob_enable label"
msgid "Enable Prime Blob"
msgstr "Primeblob inschakelen"

msgctxt "prime_tower_enable label"
msgid "Enable Prime Tower"
msgstr "Primepijler Inschakelen"

msgctxt "cool_fan_enabled label"
msgid "Enable Print Cooling"
msgstr "Koelen van de Print Inschakelen"

msgctxt "ppr_enable label"
msgid "Enable Print Process Reporting"
msgstr "Rapportage van printproces inschakelen"

msgctxt "retraction_enable label"
msgid "Enable Retraction"
msgstr "Intrekken Inschakelen"

msgctxt "support_brim_enable label"
msgid "Enable Support Brim"
msgstr "Supportbrim inschakelen"

msgctxt "support_bottom_enable label"
msgid "Enable Support Floor"
msgstr "Supportvloer inschakelen"

msgctxt "support_interface_enable label"
msgid "Enable Support Interface"
msgstr "Verbindingsstructuur Inschakelen"

msgctxt "support_roof_enable label"
msgid "Enable Support Roof"
msgstr "Supportdak inschakelen"

msgctxt "acceleration_travel_enabled label"
msgid "Enable Travel Acceleration"
msgstr "Bewegingsacceleratie inschakelen"

msgctxt "jerk_travel_enabled label"
msgid "Enable Travel Jerk"
msgstr "Bewegingsschok inschakelen"

msgctxt "ooze_shield_enabled description"
msgid "Enable exterior ooze shield. This will create a shell around the model which is likely to wipe a second nozzle if it's at the same height as the first nozzle."
msgstr "Hiermee wordt het uitloopscherm aan de buitenkant ingeschakeld, waardoor een shell rond het model wordt gemaakt waarop een tweede nozzle kan worden afgeveegd als deze zich op dezelfde hoogte bevindt als de eerste nozzle."

msgctxt "ppr_enable description"
msgid "Enable print process reporting for setting threshold values for possible fault detection."
msgstr "Rapportage van printproces inschakelen voor het instellen van drempelwaarden voor mogelijke foutdetectie."

msgctxt "small_skin_on_surface description"
msgid "Enable small (up to 'Small Top/Bottom Width') regions on the topmost skinned layer (exposed to air) to be filled with walls instead of the default pattern."
msgstr "Laat kleine (tot 'Breedte kleine bovenkant/onderkant') gebieden op de bovenste skinned layer (blootgesteld aan lucht) opvullen met muren in plaats van het standaardpatroon."

msgctxt "jerk_enabled description"
msgid "Enables adjusting the jerk of print head when the velocity in the X or Y axis changes. Increasing the jerk can reduce printing time at the cost of print quality."
msgstr "Hiermee stelt u de schok van de printkop in wanneer de snelheid in de X- of Y-as verandert. Door het verhogen van de schok wordt de printtijd mogelijk verkort ten koste van de printkwaliteit."

msgctxt "acceleration_enabled description"
msgid "Enables adjusting the print head acceleration. Increasing the accelerations can reduce printing time at the cost of print quality."
msgstr "Hiermee stelt u de printkopacceleratie in. Door het verhogen van de acceleratie wordt de printtijd mogelijk verkort ten koste van de printkwaliteit."

msgctxt "cool_fan_enabled description"
msgid "Enables the print cooling fans while printing. The fans improve print quality on layers with short layer times and bridging / overhangs."
msgstr "Hiermee schakelt u de printkoelventilatoren in tijdens het printen. De ventilatoren verbeteren de printkwaliteit van lagen met een korte laagtijd en brugvorming/overhang."

msgctxt "machine_end_gcode label"
msgid "End G-code"
msgstr "Eind G-code"

msgctxt "material_end_of_filament_purge_length label"
msgid "End of Filament Purge Length"
msgstr "Afvoerduur einde van filament"

msgctxt "material_end_of_filament_purge_speed label"
msgid "End of Filament Purge Speed"
msgstr "Afvoersnelheid einde van filament"

msgctxt "brim_replaces_support description"
msgid "Enforce brim to be printed around the model even if that space would otherwise be occupied by support. This replaces some regions of the first layer of support by brim regions."
msgstr "Dwing af dat de brim rond het model wordt geprint, zelfs als deze ruimte anders door supportstructuur zou worden ingenomen. Hierdoor worden enkele gebieden van de eerste supportlaag vervangen door brimgebieden."

msgctxt "brim_location option everywhere"
msgid "Everywhere"
msgstr "Overal"

msgctxt "support_type option everywhere"
msgid "Everywhere"
msgstr "Overal"

msgctxt "slicing_tolerance option exclusive"
msgid "Exclusive"
msgstr "Exclusief"

msgctxt "experimental label"
msgid "Experimental"
msgstr "Experimenteel"

msgctxt "z_seam_corner option z_seam_corner_outer"
msgid "Expose Seam"
msgstr "Naad zichtbaar maken"

msgctxt "meshfix_extensive_stitching label"
msgid "Extensive Stitching"
msgstr "Uitgebreid Hechten"

msgctxt "meshfix_extensive_stitching description"
msgid "Extensive stitching tries to stitch up open holes in the mesh by closing the hole with touching polygons. This option can introduce a lot of processing time."
msgstr "Met uitgebreid hechten worden zo veel mogelijk open gaten in het raster gehecht doordat het gat wordt gedicht met polygonen die elkaar raken. Deze optie kan de verwerkingstijd aanzienlijk verlengen."

msgctxt "infill_wall_line_count label"
msgid "Extra Infill Wall Count"
msgstr "Aantal Extra Wanden Rond vulling"

msgctxt "skin_outline_count label"
msgid "Extra Skin Wall Count"
msgstr "Aantal Extra Wandlijnen Rond Skin"

msgctxt "switch_extruder_extra_prime_amount description"
msgid "Extra material to prime after nozzle switching."
msgstr "Extra primemateriaal na het wisselen van de nozzle."

msgctxt "extruder_prime_pos_x label"
msgid "Extruder Prime X Position"
msgstr "X-positie voor Primen Extruder"

msgctxt "extruder_prime_pos_y label"
msgid "Extruder Prime Y Position"
msgstr "Y-positie voor Primen Extruder"

msgctxt "extruder_prime_pos_z label"
msgid "Extruder Prime Z Position"
msgstr "Z-positie voor Primen Extruder"

msgctxt "machine_extruders_share_heater label"
msgid "Extruders Share Heater"
msgstr "Extruders delen verwarming"

msgctxt "machine_extruders_share_nozzle label"
msgid "Extruders Share Nozzle"
msgstr "Extruders delen nozzle"

msgctxt "material_extrusion_cool_down_speed label"
msgid "Extrusion Cool Down Speed Modifier"
msgstr "Aanpassing Afkoelsnelheid Doorvoer"

msgctxt "speed_equalize_flow_width_factor description"
msgid "Extrusion width based correction factor on the speed. At 0% the movement speed is kept constant at the Print Speed. At 100% the movement speed is adjusted so that the flow (in mm³/s) is kept constant, i.e. lines half the normal Line Width are printed twice as fast and lines twice as wide are printed half as fast. A value larger than 100% can help to compensate for the higher pressure required to extrude wide lines."
msgstr "Op doorvoerbreedte gebaseerde correctiefactor voor de snelheid. Op 0% wordt de bewegingssnelheid gelijk gehouden aan de printsnelheid. Op 100% wordt de bewegingssnelheid zo aangepast dat de stroom (in mm³/s) constant is, d.w.z. dat alle lijnen die half zo breed zijn als de normale lijnbreedte, tweemaal zo snel worden geprint en lijnen die twee maal zo breed zijn, half zo snel. Een waarde groter dan 100% kan de hogere druk compenseren die nodig is voor de extrusie van brede lijnen."

msgctxt "cool_fan_speed label"
msgid "Fan Speed"
msgstr "Ventilatorsnelheid"

msgctxt "support_fan_enable label"
msgid "Fan Speed Override"
msgstr "Ventilatorsnelheid Overschrijven"

msgctxt "small_feature_max_length description"
msgid "Feature outlines that are shorter than this length will be printed using Small Feature Speed."
msgstr "Kenmerkcontouren die korter zijn dan deze lengte, worden afgedrukt met behulp van Klein kenmerksnelheid."

msgctxt "experimental description"
msgid "Features that haven't completely been fleshed out yet."
msgstr "Functies die nog niet volledig zijn uitgewerkt."

msgctxt "machine_feeder_wheel_diameter label"
msgid "Feeder Wheel Diameter"
msgstr "Diameter van het feedertandwiel"

msgctxt "material_final_print_temperature label"
msgid "Final Printing Temperature"
msgstr "Eindtemperatuur voor printen"

msgctxt "machine_firmware_retract label"
msgid "Firmware Retraction"
msgstr "Intrekken via firmware"

msgctxt "support_extruder_nr_layer_0 label"
msgid "First Layer Support Extruder"
msgstr "Extruder Eerste Laag van Support"

msgctxt "material_flow label"
msgid "Flow"
msgstr "Doorvoer"

msgctxt "speed_equalize_flow_width_factor label"
msgid "Flow Equalization Ratio"
msgstr "Verhouding voor afstemmen doorvoer"

msgctxt "flow_anomaly_limit label"
msgid "Flow Limit"
msgstr "Flowlimiet"

msgctxt "flow_rate_extrusion_offset_factor label"
msgid "Flow Rate Compensation Factor"
msgstr "Doorvoercompensatiefactor"

msgctxt "flow_rate_max_extrusion_offset label"
msgid "Flow Rate Compensation Max Extrusion Offset"
msgstr "Maximale extrusieoffset voor doorvoercompensatie"

msgctxt "material_flow_temp_graph label"
msgid "Flow Temperature Graph"
msgstr "Grafiek Doorvoertemperatuur"

msgctxt "flow_warn_limit label"
msgid "Flow Warning"
msgstr "Flow-waarschuwing"

msgctxt "material_flow_layer_0 description"
msgid "Flow compensation for the first layer: the amount of material extruded on the initial layer is multiplied by this value."
msgstr "Doorvoercompensatie voor de eerste laag: de hoeveelheid materiaal die voor de eerste laag wordt doorgevoerd, wordt vermenigvuldigd met deze waarde."

msgctxt "skin_material_flow_layer_0 description"
msgid "Flow compensation on bottom lines of the first layer"
msgstr "Stroomcompensatie op de onderste lijnen van de eerste laag"

msgctxt "infill_material_flow description"
msgid "Flow compensation on infill lines."
msgstr "Doorvoercompensatie op vullijnen."

msgctxt "support_interface_material_flow description"
msgid "Flow compensation on lines of support roof or floor."
msgstr "Doorvoercompensatie op de lijnen van supportdak of de supportvloer."

msgctxt "roofing_material_flow description"
msgid "Flow compensation on lines of the areas at the top of the print."
msgstr "Doorvoercompensatie op lijnen van de gebieden bovenaan de print."

msgctxt "prime_tower_flow description"
msgid "Flow compensation on prime tower lines."
msgstr "Doorvoercompensatie op primepijlerlijnen."

msgctxt "skirt_brim_material_flow description"
msgid "Flow compensation on skirt or brim lines."
msgstr "Doorvoercompensatie op skirt- of brimlijnen."

msgctxt "support_bottom_material_flow description"
msgid "Flow compensation on support floor lines."
msgstr "Doorvoercompensatie op de supportvloerlijnen."

msgctxt "support_roof_material_flow description"
msgid "Flow compensation on support roof lines."
msgstr "Doorvoercompensatie op supportdaklijnen."

msgctxt "support_material_flow description"
msgid "Flow compensation on support structure lines."
msgstr "Doorvoercompensatie op de supportstructuurlijnen."

msgctxt "wall_0_material_flow_layer_0 description"
msgid "Flow compensation on the outermost wall line of the first layer."
msgstr "Stroomcompensatie op de buitenste wandlijn van de eerste laag."

msgctxt "wall_0_material_flow description"
msgid "Flow compensation on the outermost wall line."
msgstr "Doorvoercompensatie op de buitenste wandlijn."

msgctxt "wall_0_material_flow_roofing description"
msgid "Flow compensation on the top surface outermost wall line."
msgstr "Stroomcompensatie op de buitenste wand van het bovenoppervlak."

msgctxt "wall_x_material_flow_roofing description"
msgid "Flow compensation on top surface wall lines for all wall lines except the outermost one."
msgstr "Stroomcompensatie op de wandlijnen van het bovenoppervlak voor alle muurlijnen behalve de buitenste."

msgctxt "skin_material_flow description"
msgid "Flow compensation on top/bottom lines."
msgstr "Doorvoercompensatie op bovenste/onderste lijn."

msgctxt "wall_x_material_flow_layer_0 description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one, but only for the first layer"
msgstr "Stroomcompensatie op wandlijnen voor alle wandlijnen behalve de buitenste, maar alleen voor de eerste laag"

msgctxt "wall_x_material_flow description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one."
msgstr "Doorvoercompensatie op wandlijnen voor alle wandlijnen behalve de buitenste."

msgctxt "wall_material_flow description"
msgid "Flow compensation on wall lines."
msgstr "Doorvoercompensatie op wandlijnen."

msgctxt "material_flow description"
msgid "Flow compensation: the amount of material extruded is multiplied by this value."
msgstr "Doorvoercompensatie: de hoeveelheid materiaal die wordt doorgevoerd, wordt vermenigvuldigd met deze waarde."

msgctxt "meshfix_fluid_motion_angle label"
msgid "Fluid Motion Angle"
msgstr "Hoek vloeiende beweging"

msgctxt "meshfix_fluid_motion_shift_distance label"
msgid "Fluid Motion Shift Distance"
msgstr "Vloeiende beweging verschuivingsafstand"

msgctxt "meshfix_fluid_motion_small_distance label"
msgid "Fluid Motion Small Distance"
msgstr "Vloeiende beweging kleine afstand"

msgctxt "material_flush_purge_length label"
msgid "Flush Purge Length"
msgstr "Afvoerduur flush"

msgctxt "material_flush_purge_speed label"
msgid "Flush Purge Speed"
msgstr "Afvoersnelheid flush"

msgctxt "min_wall_line_width description"
msgid "For thin structures around once or twice the nozzle size, the line widths need to be altered to adhere to the thickness of the model. This setting controls the minimum line width allowed for the walls. The minimum line widths inherently also determine the maximum line widths, since we transition from N to N+1 walls at some geometry thickness where the N walls are wide and the N+1 walls are narrow. The widest possible wall line is twice the Minimum Wall Line Width."
msgstr "Bij dunne structuren die ongeveer één of tweemaal zo groot als de nozzle zijn, moeten de lijnbreedtes worden aangepast aan de dikte van het model. Met deze instelling beheert u de minimum lijnbreedte die voor wanden is toegestaan. De minimum lijnbreedte bepaalt automatisch ook de maximale lijnbreedte, omdat we bij een bepaalde geometriedikte overgaan van wanden van N naar wanden van N+1, waarbij de N-wanden breed zijn en de N+1-wanden smal. De breedst mogelijke wandlijn is tweemaal de minimumbreedte van de wandlijn."

msgctxt "z_seam_position option front"
msgid "Front"
msgstr "Voor"

msgctxt "z_seam_position option frontleft"
msgid "Front Left"
msgstr "Linksvoor"

msgctxt "z_seam_position option frontright"
msgid "Front Right"
msgstr "Rechtsvoor"

msgctxt "draft_shield_height_limitation option full"
msgid "Full"
msgstr "Volledig"

msgctxt "magic_fuzzy_skin_enabled label"
msgid "Fuzzy Skin"
msgstr "Rafelig Oppervlak"

msgctxt "magic_fuzzy_skin_point_density label"
msgid "Fuzzy Skin Density"
msgstr "Dichtheid Rafelig Oppervlak"

msgctxt "magic_fuzzy_skin_outside_only label"
msgid "Fuzzy Skin Outside Only"
msgstr "Alleen rafelig oppervlak buitenkant"

msgctxt "magic_fuzzy_skin_point_dist label"
msgid "Fuzzy Skin Point Distance"
msgstr "Puntafstand Rafelig Oppervlak"

msgctxt "magic_fuzzy_skin_thickness label"
msgid "Fuzzy Skin Thickness"
msgstr "Dikte Rafelig Oppervlak"

msgctxt "machine_gcode_flavor label"
msgid "G-code Flavor"
msgstr "Versie G-code"

msgctxt "machine_end_gcode description"
msgid "G-code commands to be executed at the very end - separated by \n."
msgstr "G-code-opdrachten die aan het eind worden uitgevoerd, gescheiden door "
"."

msgctxt "machine_start_gcode description"
msgid "G-code commands to be executed at the very start - separated by \n."
msgstr "G-code-opdrachten die aan het begin worden uitgevoerd, gescheiden door "
"."

msgctxt "material_guid description"
msgid "GUID of the material. This is set automatically."
msgstr "GUID van het materiaal. Deze optie wordt automatisch ingesteld."

msgctxt "gantry_height label"
msgid "Gantry Height"
msgstr "Rijbrughoogte"

msgctxt "interlocking_enable label"
msgid "Generate Interlocking Structure"
msgstr "Genereer in elkaar grijpende structuur"

msgctxt "support_enable label"
msgid "Generate Support"
msgstr "Support genereren"

msgctxt "support_brim_enable description"
msgid "Generate a brim within the support infill regions of the first layer. This brim is printed underneath the support, not around it. Enabling this setting increases the adhesion of support to the build plate."
msgstr "Genereer een brim binnen de supportvulgebieden van de eerste laag. Deze brim wordt niet rondom maar onder de supportstructuur geprint. Als u deze instelling inschakelt, hecht de supportstructuur beter aan het platform."

msgctxt "support_interface_enable description"
msgid "Generate a dense interface between the model and the support. This will create a skin at the top of the support on which the model is printed and at the bottom of the support, where it rests on the model."
msgstr "Hiermee maakt u een dichte verbindingsstructuur tussen het model en de supportstructuur. Er wordt een skin gemaakt aan de bovenkant van de supportstructuur waarop het model wordt geprint en op de bodem van de supportstructuur waar dit op het model rust."

msgctxt "support_bottom_enable description"
msgid "Generate a dense slab of material between the bottom of the support and the model. This will create a skin between the model and support."
msgstr "Genereer een dichte materiaallaag tussen de onderzijde van de supportstructuur en het model. Hierdoor wordt een skin gemaakt tussen het model en de supportstructuur."

msgctxt "support_roof_enable description"
msgid "Generate a dense slab of material between the top of support and the model. This will create a skin between the model and support."
msgstr "Genereer een dichte materiaallaag tussen de bovenzijde van de supportstructuur en het model. Hierdoor wordt een skin gemaakt tussen het model en de supportstructuur."

msgctxt "support_enable description"
msgid "Generate structures to support parts of the model which have overhangs. Without these structures, such parts would collapse during printing."
msgstr "Genereer structuren om delen van het model met overhang te ondersteunen. Zonder deze structuren zakken dergelijke delen in tijdens het printen."

msgctxt "machine_buildplate_type option glass"
msgid "Glass"
msgstr "Glas"

msgctxt "ironing_enabled description"
msgid "Go over the top surface one additional time, but this time extruding very little material. This is meant to melt the plastic on top further, creating a smoother surface. The pressure in the nozzle chamber is kept high so that the creases in the surface are filled with material."
msgstr "Nog een extra keer over de bovenlaag gaan, dit keer zonder veel materiaal te extruderen. Hierdoor wordt de kunststof aan de bovenkant verder gesmolten, waardoor een gladder oppervlak wordt verkregen. De kamerdruk in de nozzle wordt hoog gehouden zodat de spleten in het oppervlak met materiaal worden gevuld."

msgctxt "gradual_infill_step_height label"
msgid "Gradual Infill Step Height"
msgstr "Staphoogte Geleidelijke Vulling"

msgctxt "gradual_infill_steps label"
msgid "Gradual Infill Steps"
msgstr "Stappen Geleidelijke Vulling"

msgctxt "gradual_support_infill_step_height label"
msgid "Gradual Support Infill Step Height"
msgstr "Geleidelijke supportvulling hoogte traptreden"

msgctxt "gradual_support_infill_steps label"
msgid "Gradual Support Infill Steps"
msgstr "Geleidelijke supportvulling traptreden"

msgctxt "cool_min_temperature description"
msgid "Gradually reduce to this temperature when printing at reduced speeds because of minimum layer time."
msgstr "Verlaag geleidelijk naar deze temperatuur bij het printen met lagere snelheden vanwege de minimale laagtijd."

msgctxt "infill_pattern option grid"
msgid "Grid"
msgstr "Raster"

msgctxt "support_bottom_pattern option grid"
msgid "Grid"
msgstr "Raster"

msgctxt "support_interface_pattern option grid"
msgid "Grid"
msgstr "Raster"

msgctxt "support_pattern option grid"
msgid "Grid"
msgstr "Raster"

msgctxt "support_roof_pattern option grid"
msgid "Grid"
msgstr "Raster"

msgctxt "machine_gcode_flavor option Griffin"
msgid "Griffin"
msgstr "Griffin"

msgctxt "group_outer_walls label"
msgid "Group Outer Walls"
msgstr "Groepeer de buitenwanden"

msgctxt "infill_pattern option gyroid"
msgid "Gyroid"
msgstr "Gyroïde"

msgctxt "support_pattern option gyroid"
msgid "Gyroid"
msgstr "Gyroïde"

msgctxt "machine_heated_build_volume label"
msgid "Has Build Volume Temperature Stabilization"
msgstr "Heeft temperatuurstabilisatie van werkvolume"

msgctxt "machine_heated_bed label"
msgid "Has Heated Build Plate"
msgstr "Heeft verwarmd platform"

msgctxt "machine_nozzle_heat_up_speed label"
msgid "Heat Up Speed"
msgstr "Verwarmingssnelheid"

msgctxt "machine_heat_zone_length label"
msgid "Heat Zone Length"
msgstr "Lengte verwarmingszone"

msgctxt "draft_shield_height description"
msgid "Height limitation of the draft shield. Above this height no draft shield will be printed."
msgstr "Stel een hoogtebeperking in voor het tochtscherm. Boven deze hoogte wordt er geen tochtscherm geprint."

msgctxt "z_seam_corner option z_seam_corner_inner"
msgid "Hide Seam"
msgstr "Naad verbergen"

msgctxt "z_seam_corner option z_seam_corner_any"
msgid "Hide or Expose Seam"
msgstr "Naad verbergen of zichtbaar maken"

msgctxt "hole_xy_offset label"
msgid "Hole Horizontal Expansion"
msgstr "Horizontale uitbreiding gaten"

msgctxt "hole_xy_offset_max_diameter label"
msgid "Hole Horizontal Expansion Max Diameter"
msgstr "Gat horizontale expansie max diameter"

msgctxt "small_hole_max_size description"
msgid "Holes and part outlines with a diameter smaller than this will be printed using Small Feature Speed."
msgstr "Gaten en contouren van onderdelen met een kleinere diameter dan deze worden afgedrukt met behulp van Klein kenmerksnelheid."

msgctxt "xy_offset label"
msgid "Horizontal Expansion"
msgstr "Horizontale Uitbreiding"

msgctxt "material_shrinkage_percentage_xy label"
msgid "Horizontal Scaling Factor Shrinkage Compensation"
msgstr "Horizontale schaalfactor krimpcompensatie"

msgctxt "material_break_preparation_retracted_position description"
msgid "How far the filament can be stretched before it breaks, while heated."
msgstr "Hoe ver het filament kan worden uitgerekt voordat het afbreekt, wanneer het wordt verwarmd."

msgctxt "material_anti_ooze_retracted_position description"
msgid "How far the material needs to be retracted before it stops oozing."
msgstr "Hoe ver het materiaal moet worden ingetrokken voordat het niet meer uitloopt."

msgctxt "flow_rate_extrusion_offset_factor description"
msgid "How far to move the filament in order to compensate for changes in flow rate, as a percentage of how far the filament would move in one second of extrusion."
msgstr "Hoe ver het filament moet worden verplaatst om veranderingen in de stroomsnelheid te compenseren, als een percentage van hoe ver het filament in één seconde extrusie zou bewegen."

msgctxt "material_break_retracted_position description"
msgid "How far to retract the filament in order to break it cleanly."
msgstr "Hoe ver het filament moet worden ingetrokken om het recht af te breken."

msgctxt "material_break_preparation_speed description"
msgid "How fast the filament needs to be retracted just before breaking it off in a retraction."
msgstr "Hoe snel het filament moet worden ingetrokken voordat het bij het intrekken afbreekt."

msgctxt "material_anti_ooze_retraction_speed description"
msgid "How fast the material needs to be retracted during a filament switch to prevent oozing."
msgstr "Hoe snel het materiaal moet worden ingetrokken tijdens het wisselen van een filament om uitlopen te voorkomen."

msgctxt "material_end_of_filament_purge_speed description"
msgid "How fast to prime the material after replacing an empty spool with a fresh spool of the same material."
msgstr "Hoe snel het materiaal moet worden geprimed na het vervangen van een lege spoel door een nieuwe spoel van hetzelfde materiaal."

msgctxt "material_flush_purge_speed description"
msgid "How fast to prime the material after switching to a different material."
msgstr "Hoe snel het materiaal moet worden geprimed na het overschakelen op een ander materiaal."

msgctxt "material_maximum_park_duration description"
msgid "How long the material can be kept out of dry storage safely."
msgstr "Hoe lang het materiaal veilig buiten een droge opslagplaats kan worden bewaard."

msgctxt "machine_steps_per_mm_x description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the X direction."
msgstr "Hoeveel stappen van de stappenmotor nodig zijn voor een verplaatsing van één millimeter in de X-richting."

msgctxt "machine_steps_per_mm_y description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Y direction."
msgstr "Hoeveel stappen van de stappenmotor nodig zijn voor een verplaatsing van één millimeter in de Y-richting."

msgctxt "machine_steps_per_mm_z description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Z direction."
msgstr "Hoeveel stappen van de stappenmotor nodig zijn voor een verplaatsing van één millimeter in de Z-richting."

msgctxt "machine_steps_per_mm_e description"
msgid "How many steps of the stepper motors will result in moving the feeder wheel by one millimeter around its circumference."
msgstr "Hoeveel stappen van de stappenmotor nodig zijn voor een verplaatsing van het feederwiel van één millimeter rond de omtrek."

msgctxt "material_end_of_filament_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when replacing an empty spool with a fresh spool of the same material."
msgstr "Hoeveel materiaal moet worden gebruikt om het vorige materiaal uit de nozzle te verwijderen (in lengte filament) bij het vervangen van een lege spoel door een nieuwe spoel van hetzelfde materiaal."

msgctxt "material_flush_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when switching to a different material."
msgstr "Hoeveel materiaal moet worden gebruikt om het vorige materiaal uit de nozzle te verwijderen (in lengte filament) bij het overschakelen op een ander materiaal."

msgctxt "machine_extruders_shared_nozzle_initial_retraction description"
msgid "How much the filament of each extruder is assumed to have been retracted from the shared nozzle tip at the completion of the printer-start gcode script; the value should be equal to or greater than the length of the common part of the nozzle's ducts."
msgstr "Hoever het filament van elke extruder geacht wordt te zijn ingetrokken vanuit de gedeelde nozzle als het G-code-script voor het opstarten van de printer is uitgevoerd. De waarde mag niet gelijk zijn aan of groter zijn dan de lengte van het gemeenschappelijke deel van de kanalen in de nozzle."

msgctxt "support_interface_priority description"
msgid "How support interface and support will interact when they overlap. Currently only implemented for support roof."
msgstr "Hoe ondersteuningsinterface en ondersteuning op elkaar inwerken wanneer ze elkaar overlappen. Momenteel alleen geïmplementeerd voor ondersteunend dak."

msgctxt "support_tree_min_height_to_model description"
msgid "How tall a branch has to be if it is placed on the model. Prevents small blobs of support. This setting is ignored when a branch is supporting a support roof."
msgstr "Hoe groot moet een tak zijn als deze op het model wordt geplaatst. Voorkomt kleine ondersteunende blobs. Deze instelling wordt genegeerd wanneer een tak een ondersteunend dak ondersteunt."

msgctxt "bridge_skin_support_threshold description"
msgid "If a skin region is supported for less than this percentage of its area, print it using the bridge settings. Otherwise it is printed using the normal skin settings."
msgstr "Als voor een skinregio minder supportstructuur wordt geprint dan dit percentage van zijn oppervlakte, print u dit met de bruginstellingen. Anders wordt er geprint met de normale skininstellingen."

msgctxt "meshfix_fluid_motion_angle description"
msgid "If a toolpath-segment deviates more than this angle from the general motion it is smoothed."
msgstr "Als een baansegment meer dan deze hoek afwijkt van de algemene beweging, wordt hij afgevlakt."

msgctxt "bridge_enable_more_layers description"
msgid "If enabled, the second and third layers above the air are printed using the following settings. Otherwise, those layers are printed using the normal settings."
msgstr "Als deze optie ingeschakeld is, worden de tweede en derde laag boven de vrije ruimte geprint met de volgende instellingen. Anders worden de lagen geprint met de normale instellingen."

msgctxt "wall_transition_filter_distance description"
msgid "If it would be transitioning back and forth between different numbers of walls in quick succession, don't transition at all. Remove transitions if they are closer together than this distance."
msgstr "Maak geen gebruik van wandovergangen als dit leidt tot snelle achtereenvolgende veranderingen in het aantal wanden. Verwijder overgangen als de afstand tussen overgangen kleiner is dan deze afstand."

msgctxt "raft_base_margin description"
msgid "If the raft base is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Als het vlot op de basis is ingeschakeld, is dit het extra vlotgebied rond het model dat ook een vlot krijgt. Als u deze marge vergroot, wordt er een sterker vlot gecreëerd, hoewel er meer materiaal gebruikt wordt en er minder plek overblijft voor uw print."

msgctxt "raft_margin description"
msgid "If the raft is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Als de raft is ingeschakeld, is dit het extra raftgebied rond het model dat ook van een raft wordt voorzien. Als u deze marge vergroot, krijgt u een stevigere raft, maar gebruikt u ook meer materiaal en houdt u minder ruimte over voor de print."

msgctxt "raft_interface_margin description"
msgid "If the raft middle is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Als het vlot in het midden is ingeschakeld, is dit het extra vlotgebied rond het model dat ook een vlot krijgt. Als u deze marge vergroot, wordt er een sterker vlot gecreëerd, hoewel er meer materiaal gebruikt wordt en er minder plek overblijft voor uw print."

msgctxt "raft_surface_margin description"
msgid "If the raft top is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Als het vlot aan de bovenkant is ingeschakeld, is dit het extra vlotebied rond het model dat ook een vlot krijgt. Als u deze marge vergroot, wordt er een sterker vlot gecreëerd, hoewel er meer materiaal gebruikt wordt en er minder plek overblijft voor uw print."

msgctxt "meshfix_union_all description"
msgid "Ignore the internal geometry arising from overlapping volumes within a mesh and print the volumes as one. This may cause unintended internal cavities to disappear."
msgstr "Negeer de interne geometrie die ontstaat uit overlappende volumes binnen een raster en print de volumes als een geheel. Hiermee kunnen onbedoelde holtes binnenin verdwijnen."

msgctxt "material_bed_temp_prepend label"
msgid "Include Build Plate Temperature"
msgstr "Platformtemperatuur invoegen"

msgctxt "material_print_temp_prepend label"
msgid "Include Material Temperatures"
msgstr "Materiaaltemperatuur invoegen"

msgctxt "slicing_tolerance option inclusive"
msgid "Inclusive"
msgstr "Inclusief"

msgctxt "infill description"
msgid "Infill"
msgstr "Vulling"

msgctxt "infill label"
msgid "Infill"
msgstr "Vulling"

msgctxt "acceleration_infill label"
msgid "Infill Acceleration"
msgstr "Vulacceleratie"

msgctxt "infill_before_walls label"
msgid "Infill Before Walls"
msgstr "Vulling vóór Wanden"

msgctxt "infill_sparse_density label"
msgid "Infill Density"
msgstr "Dichtheid Vulling"

msgctxt "infill_extruder_nr label"
msgid "Infill Extruder"
msgstr "Vullingextruder"

msgctxt "infill_material_flow label"
msgid "Infill Flow"
msgstr "Doorvoer vulling"

msgctxt "jerk_infill label"
msgid "Infill Jerk"
msgstr "Vulschok"

msgctxt "infill_sparse_thickness label"
msgid "Infill Layer Thickness"
msgstr "Dikte Vullaag"

msgctxt "infill_angles label"
msgid "Infill Line Directions"
msgstr "Lijnrichting vulling"

msgctxt "infill_line_distance label"
msgid "Infill Line Distance"
msgstr "Lijnafstand Vulling"

msgctxt "infill_multiplier label"
msgid "Infill Line Multiplier"
msgstr "Vermenigvuldiging Vullijn"

msgctxt "infill_line_width label"
msgid "Infill Line Width"
msgstr "Lijnbreedte Vulling"

msgctxt "infill_mesh label"
msgid "Infill Mesh"
msgstr "Vulraster"

msgctxt "infill_support_angle label"
msgid "Infill Overhang Angle"
msgstr "Overhanghoek vulling"

msgctxt "infill_overlap_mm label"
msgid "Infill Overlap"
msgstr "Overlap Vulling"

msgctxt "infill_overlap label"
msgid "Infill Overlap Percentage"
msgstr "Overlappercentage vulling"

msgctxt "infill_pattern label"
msgid "Infill Pattern"
msgstr "Vulpatroon"

msgctxt "speed_infill label"
msgid "Infill Speed"
msgstr "Vulsnelheid"

msgctxt "infill_support_enabled label"
msgid "Infill Support"
msgstr "Supportvulling"

msgctxt "infill_enable_travel_optimization label"
msgid "Infill Travel Optimization"
msgstr "Bewegingsoptimalisatie vulling"

msgctxt "infill_wipe_dist label"
msgid "Infill Wipe Distance"
msgstr "Veegafstand Vulling"

msgctxt "infill_offset_x label"
msgid "Infill X Offset"
msgstr "Vulling X-offset"

msgctxt "infill_offset_y label"
msgid "Infill Y Offset"
msgstr "Vulling Y-offset"

msgctxt "initial_bottom_layers label"
msgid "Initial Bottom Layers"
msgstr "Eerste onderste lagen"

msgctxt "cool_fan_speed_0 label"
msgid "Initial Fan Speed"
msgstr "Startsnelheid ventilator"

msgctxt "acceleration_layer_0 label"
msgid "Initial Layer Acceleration"
msgstr "Acceleratie Eerste Laag"

msgctxt "skin_material_flow_layer_0 label"
msgid "Initial Layer Bottom Flow"
msgstr "Initiële laag onderste lijn"

msgctxt "support_tree_bp_diameter label"
msgid "Initial Layer Diameter"
msgstr "Diameter beginlaag"

msgctxt "material_flow_layer_0 label"
msgid "Initial Layer Flow"
msgstr "Doorvoer eerste laag"

msgctxt "layer_height_0 label"
msgid "Initial Layer Height"
msgstr "Hoogte Eerste Laag"

msgctxt "xy_offset_layer_0 label"
msgid "Initial Layer Horizontal Expansion"
msgstr "Eerste laag Horizontale uitbreiding"

msgctxt "wall_x_material_flow_layer_0 label"
msgid "Initial Layer Inner Wall Flow"
msgstr "Initiële laag binnenwandstroom"

msgctxt "jerk_layer_0 label"
msgid "Initial Layer Jerk"
msgstr "Schok Eerste Laag"

msgctxt "initial_layer_line_width_factor label"
msgid "Initial Layer Line Width"
msgstr "Lijnbreedte eerste laag"

msgctxt "wall_0_material_flow_layer_0 label"
msgid "Initial Layer Outer Wall Flow"
msgstr "Initiële laag buitenwandstroom"

msgctxt "acceleration_print_layer_0 label"
msgid "Initial Layer Print Acceleration"
msgstr "Printacceleratie Eerste Laag"

msgctxt "jerk_print_layer_0 label"
msgid "Initial Layer Print Jerk"
msgstr "Printschok Eerste Laag"

msgctxt "speed_print_layer_0 label"
msgid "Initial Layer Print Speed"
msgstr "Printsnelheid Eerste Laag"

msgctxt "speed_layer_0 label"
msgid "Initial Layer Speed"
msgstr "Snelheid Eerste Laag"

msgctxt "support_initial_layer_line_distance label"
msgid "Initial Layer Support Line Distance"
msgstr "Lijnafstand Supportstructuur Eerste Laag"

msgctxt "acceleration_travel_layer_0 label"
msgid "Initial Layer Travel Acceleration"
msgstr "Bewegingsacceleratie Eerste Laag"

msgctxt "jerk_travel_layer_0 label"
msgid "Initial Layer Travel Jerk"
msgstr "Bewegingsschok Eerste Laag"

msgctxt "speed_travel_layer_0 label"
msgid "Initial Layer Travel Speed"
msgstr "Bewegingssnelheid Eerste Laag"

msgctxt "layer_0_z_overlap label"
msgid "Initial Layer Z Overlap"
msgstr "Z Overlap Eerste Laag"

msgctxt "material_initial_print_temperature label"
msgid "Initial Printing Temperature"
msgstr "Starttemperatuur voor printen"

msgctxt "acceleration_wall_x label"
msgid "Inner Wall Acceleration"
msgstr "Binnenwandacceleratie"

msgctxt "wall_x_extruder_nr label"
msgid "Inner Wall Extruder"
msgstr "Extruder binnenwand"

msgctxt "jerk_wall_x label"
msgid "Inner Wall Jerk"
msgstr "Schok Binnenwand"

msgctxt "speed_wall_x label"
msgid "Inner Wall Speed"
msgstr "Snelheid Binnenwand"

msgctxt "wall_x_material_flow label"
msgid "Inner Wall(s) Flow"
msgstr "Doorvoer binnenwand(en)"

msgctxt "wall_line_width_x label"
msgid "Inner Wall(s) Line Width"
msgstr "Lijnbreedte Binnenwand(en)"

msgctxt "wall_0_inset description"
msgid "Inset applied to the path of the outer wall. If the outer wall is smaller than the nozzle, and printed after the inner walls, use this offset to get the hole in the nozzle to overlap with the inner walls instead of the outside of the model."
msgstr "Uitsparing die wordt toegepast in de buitenwand. Als de buitenwand smaller is dan de nozzle en na de binnenwand wordt geprint, gebruikt u deze offset om het gat in de nozzle te laten overlappen met de binnenwanden in plaats van met de buitenkant van het model."

msgctxt "brim_location option inside"
msgid "Inside Only"
msgstr "Alleen binnenkant"

msgctxt "inset_direction option inside_out"
msgid "Inside To Outside"
msgstr "Van binnen naar buiten"

msgctxt "support_interface_priority option interface_lines_overwrite_support_area"
msgid "Interface lines preferred"
msgstr "Geprefereerde interfacelijnen"

msgctxt "support_interface_priority option interface_area_overwrite_support_area"
msgid "Interface preferred"
msgstr "Geprefereerde interface"

msgctxt "prime_tower_mode option interleaved"
msgid "Interleaved"
msgstr "Interleaved"

msgctxt "interlocking_beam_layer_count label"
msgid "Interlocking Beam Layer Count"
msgstr "Aantal in elkaar grijpende balklagen"

msgctxt "interlocking_beam_width label"
msgid "Interlocking Beam Width"
msgstr "Breedte in elkaar grijpende balk"

msgctxt "interlocking_boundary_avoidance label"
msgid "Interlocking Boundary Avoidance"
msgstr "In elkaar grijpende grensvermijding"

msgctxt "interlocking_depth label"
msgid "Interlocking Depth"
msgstr "In elkaar grijpende diepte"

msgctxt "interlocking_orientation label"
msgid "Interlocking Structure Orientation"
msgstr "In elkaar grijpende structuuroriëntatie"

msgctxt "ironing_only_highest_layer label"
msgid "Iron Only Highest Layer"
msgstr "Alleen hoogste laag strijken"

msgctxt "acceleration_ironing label"
msgid "Ironing Acceleration"
msgstr "Strijkacceleratie"

msgctxt "ironing_flow label"
msgid "Ironing Flow"
msgstr "Strijkdoorvoer"

msgctxt "ironing_inset label"
msgid "Ironing Inset"
msgstr "Uitsparing strijken"

msgctxt "jerk_ironing label"
msgid "Ironing Jerk"
msgstr "Schok strijken"

msgctxt "ironing_line_spacing label"
msgid "Ironing Line Spacing"
msgstr "Tussenruimte strijklijnen"

msgctxt "ironing_pattern label"
msgid "Ironing Pattern"
msgstr "Strijkpatroon"

msgctxt "speed_ironing label"
msgid "Ironing Speed"
msgstr "Strijksnelheid"

msgctxt "machine_center_is_zero label"
msgid "Is Center Origin"
msgstr "Is oorsprongpunt centraal"

msgctxt "material_is_support_material label"
msgid "Is support material"
msgstr "Is support materiaal"

msgctxt "material_crystallinity description"
msgid "Is this material the type that breaks off cleanly when heated (crystalline), or is it the type that produces long intertwined polymer chains (non-crystalline)?"
msgstr "Breekt dit materiaal recht af wanneer het wordt verwarmd (kristallijn) of produceert het lange, met elkaar verweven polymeerketens (niet-kristallijn)?"

msgctxt "material_is_support_material description"
msgid "Is this material typically used as a support material during printing."
msgstr "Wordt dit materiaal meestal gebruikt als support materiaal tijdens het printen."

msgctxt "magic_fuzzy_skin_outside_only description"
msgid "Jitter only the parts' outlines and not the parts' holes."
msgstr "Trillen alleen voor de contouren van de onderdelen en niet voor de gaten van de onderdelen."

msgctxt "meshfix_keep_open_polygons label"
msgid "Keep Disconnected Faces"
msgstr "Onderbroken Oppervlakken Behouden"

msgctxt "layer_height label"
msgid "Layer Height"
msgstr "Laaghoogte"

msgctxt "layer_start_x label"
msgid "Layer Start X"
msgstr "Begin laag X"

msgctxt "layer_start_y label"
msgid "Layer Start Y"
msgstr "Begin laag Y"

msgctxt "raft_base_thickness description"
msgid "Layer thickness of the base raft layer. This should be a thick layer which sticks firmly to the printer build plate."
msgstr "De laagdikte van de grondlaag van de raft. Deze laag moet dik zijn, zodat deze stevig hecht aan het platform."

msgctxt "raft_interface_thickness description"
msgid "Layer thickness of the middle raft layer."
msgstr "De laagdikte van de middelste laag van de raft."

msgctxt "raft_surface_thickness description"
msgid "Layer thickness of the top raft layers."
msgstr "Laagdikte van de bovenste lagen van de raft."

msgctxt "support_skip_zag_per_mm description"
msgid "Leave out a connection between support lines once every N millimeter to make the support structure easier to break away."
msgstr "Sla elke N millimeter een verbinding tussen de lijnen van de supportstructuur over, zodat deze gemakkelijker kan worden weggebroken."

msgctxt "z_seam_position option left"
msgid "Left"
msgstr "Links"

msgctxt "cool_lift_head label"
msgid "Lift Head"
msgstr "Printkop Optillen"

msgctxt "infill_pattern option lightning"
msgid "Lightning"
msgstr "Bliksem"

msgctxt "lightning_infill_overhang_angle label"
msgid "Lightning Infill Overhang Angle"
msgstr "Hoek overhang bliksemvulling"

msgctxt "lightning_infill_prune_angle label"
msgid "Lightning Infill Prune Angle"
msgstr "Snoeihoek bliksemvulling"

msgctxt "lightning_infill_straightening_angle label"
msgid "Lightning Infill Straightening Angle"
msgstr "Rechtbuighoek bliksemvulling"

msgctxt "lightning_infill_support_angle label"
msgid "Lightning Infill Support Angle"
msgstr "Hoek supportstructuur bliksemvulling"

msgctxt "support_tree_limit_branch_reach label"
msgid "Limit Branch Reach"
msgstr "Takbereik beperken"

msgctxt "support_tree_limit_branch_reach description"
msgid "Limit how far each branch should travel from the point it supports. This can make the support more sturdy, but will increase the amount of branches (and because of that material usage/print time)"
msgstr "Beperken hoe ver elke tak moet bewegen vanaf het punt dat het ondersteunt. Dit kan de steun steviger maken, maar zal de hoeveelheid takken vergroten (en daardoor het materiaalgebruik/de printtijd)."

msgctxt "bv_temp_warn_limit description"
msgid "Limit on Build Volume Temperature warning for detection."
msgstr "Limiet op waarschuwing temperatuur bouwvolume voor detectie."

msgctxt "bv_temp_anomaly_limit description"
msgid "Limit on Build Volume temperature Anomaly for detection."
msgstr "Limiet op afwijking temperatuur bouwvolume voor detectie."

msgctxt "print_temp_anomaly_limit description"
msgid "Limit on Print Temperature anomaly for detection."
msgstr "Limiet op afwijking printtemperatuur voor detectie."

msgctxt "print_temp_warn_limit description"
msgid "Limit on Print temperature warning for detection."
msgstr "Limiet voor waarschuwing printtemperatuur voor detectie."

msgctxt "flow_anomaly_limit description"
msgid "Limit on flow anomaly for detection."
msgstr "Limiet op flow-afwijking voor detectie."

msgctxt "flow_warn_limit description"
msgid "Limit on the flow warning for detection."
msgstr "Limiet op flow-waarschuwing voor detectie."

msgctxt "cutting_mesh description"
msgid "Limit the volume of this mesh to within other meshes. You can use this to make certain areas of one mesh print with different settings and with a whole different extruder."
msgstr "Beperk het volume van dit raster binnen andere rasters. U kunt dit gebruiken om bepaalde delen van een raster met andere instellingen en met een andere extruder te printen."

msgctxt "draft_shield_height_limitation option limited"
msgid "Limited"
msgstr "Beperkt"

msgctxt "line_width label"
msgid "Line Width"
msgstr "Lijnbreedte"

msgctxt "infill_pattern option lines"
msgid "Lines"
msgstr "Lijnen"

msgctxt "roofing_pattern option lines"
msgid "Lines"
msgstr "Lijnen"

msgctxt "support_bottom_pattern option lines"
msgid "Lines"
msgstr "Lijnen"

msgctxt "support_interface_pattern option lines"
msgid "Lines"
msgstr "Lijnen"

msgctxt "support_pattern option lines"
msgid "Lines"
msgstr "Lijnen"

msgctxt "support_roof_pattern option lines"
msgid "Lines"
msgstr "Lijnen"

msgctxt "top_bottom_pattern option lines"
msgid "Lines"
msgstr "Lijnen"

msgctxt "top_bottom_pattern_0 option lines"
msgid "Lines"
msgstr "Lijnen"

msgctxt "machine_gcode_flavor option MACH3"
msgid "Mach3"
msgstr "Mach3"

msgctxt "machine_settings label"
msgid "Machine"
msgstr "Machine"

msgctxt "machine_depth label"
msgid "Machine Depth"
msgstr "Machinediepte"

msgctxt "machine_head_with_fans_polygon label"
msgid "Machine Head & Fan Polygon"
msgstr "Machinekop- en ventilatorpolygoon"

msgctxt "machine_height label"
msgid "Machine Height"
msgstr "Machinehoogte"

msgctxt "machine_name label"
msgid "Machine Type"
msgstr "Type Machine"

msgctxt "machine_width label"
msgid "Machine Width"
msgstr "Machinebreedte"

msgctxt "machine_settings description"
msgid "Machine specific settings"
msgstr "Instellingen van de machine"

msgctxt "conical_overhang_enabled label"
msgid "Make Overhang Printable"
msgstr "Overhang Printbaar Maken"

msgctxt "multiple_mesh_overlap description"
msgid "Make meshes which are touching each other overlap a bit. This makes them bond together better."
msgstr "Laat rasters die elkaar raken deels met elkaar overlappen. Hierdoor hechten ze beter aan elkaar."

msgctxt "support_conical_enabled description"
msgid "Make support areas smaller at the bottom than at the overhang."
msgstr "Maak draagvlakken aan de onderkant kleiner dan bij de overhang."

msgctxt "support_mesh_drop_down description"
msgid "Make support everywhere below the support mesh, so that there's no overhang in the support mesh."
msgstr "Maak overal onder het supportraster support zodat er in het supportraster geen overhang is."

msgctxt "extruder_prime_pos_abs description"
msgid "Make the extruder prime position absolute rather than relative to the last-known location of the head."
msgstr "Maak van de primepositie van de extruder de absolute positie, in plaats van de relatieve positie ten opzichte van de laatst bekende locatie van de printkop."

msgctxt "layer_0_z_overlap description"
msgid "Make the first and second layer of the model overlap in the Z direction to compensate for the filament lost in the airgap. All models above the first model layer will be shifted down by this amount.\nIt may be noted that sometimes the second layer is printed below initial layer because of this setting. This is intended behavior"
msgstr "Laat de eerste en tweede laag van het model overlappen in de Z-richting om te compenseren voor het filament dat verloren gaat in de luchtspleet. Alle modellen boven de eerste modellaag zullen met deze hoeveelheid naar beneden worden verschoven."
"Het kan voorkomen dat de tweede laag onder de eerste laag wordt afgedrukt door deze instelling. Dit gedrag is zo bedoeld."

msgctxt "meshfix description"
msgid "Make the meshes more suited for 3D printing."
msgstr "Maak de rasters beter geschikt voor 3D-printen."

msgctxt "machine_gcode_flavor option Makerbot"
msgid "Makerbot"
msgstr "Makerbot"

msgctxt "machine_gcode_flavor option RepRap (Marlin/Sprinter)"
msgid "Marlin"
msgstr "Marlin"

msgctxt "machine_gcode_flavor option RepRap (Volumetric)"
msgid "Marlin (Volumetric)"
msgstr "Marlin (Volumetrisch)"

msgctxt "material description"
msgid "Material"
msgstr "Materiaal"

msgctxt "material label"
msgid "Material"
msgstr "Materiaal"

msgctxt "material_brand label"
msgid "Material Brand"
msgstr "Materiaalmerk"

msgctxt "material_guid label"
msgid "Material GUID"
msgstr "Materiaal-GUID"

msgctxt "material_type label"
msgid "Material Type"
msgstr "Materiaaltype"

msgctxt "max_extrusion_before_wipe label"
msgid "Material Volume Between Wipes"
msgstr "Materiaalvolume tussen afvegen"

msgctxt "retraction_combing_max_distance label"
msgid "Max Comb Distance With No Retract"
msgstr "Max. combing-afstand zonder intrekken"

msgctxt "machine_max_acceleration_x label"
msgid "Maximum Acceleration X"
msgstr "Maximale Acceleratie X"

msgctxt "machine_max_acceleration_y label"
msgid "Maximum Acceleration Y"
msgstr "Maximale Acceleratie Y"

msgctxt "machine_max_acceleration_z label"
msgid "Maximum Acceleration Z"
msgstr "Maximale Acceleratie Z"

msgctxt "support_tree_angle label"
msgid "Maximum Branch Angle"
msgstr "Maximale vertakkingshoek"

msgctxt "meshfix_maximum_deviation label"
msgid "Maximum Deviation"
msgstr "Maximale afwijking"

msgctxt "meshfix_maximum_extrusion_area_deviation label"
msgid "Maximum Extrusion Area Deviation"
msgstr "Maximale afwijking doorvoergebied"

msgctxt "cool_fan_speed_max label"
msgid "Maximum Fan Speed"
msgstr "Maximale Ventilatorsnelheid"

msgctxt "machine_max_acceleration_e label"
msgid "Maximum Filament Acceleration"
msgstr "Maximale Filamentacceleratie"

msgctxt "conical_overhang_angle label"
msgid "Maximum Model Angle"
msgstr "Maximale Modelhoek"

msgctxt "conical_overhang_hole_size label"
msgid "Maximum Overhang Hole Area"
msgstr "Maximale overhang oppervlak gat"

msgctxt "material_maximum_park_duration label"
msgid "Maximum Park Duration"
msgstr "Maximale parkeerduur"

msgctxt "meshfix_maximum_resolution label"
msgid "Maximum Resolution"
msgstr "Maximale resolutie"

msgctxt "retraction_count_max label"
msgid "Maximum Retraction Count"
msgstr "Maximaal Aantal Intrekbewegingen"

msgctxt "max_skin_angle_for_expansion label"
msgid "Maximum Skin Angle for Expansion"
msgstr "Maximale skinhoek voor uitbreiding"

msgctxt "machine_max_feedrate_e label"
msgid "Maximum Speed E"
msgstr "Maximale Snelheid E"

msgctxt "machine_max_feedrate_x label"
msgid "Maximum Speed X"
msgstr "Maximale Snelheid X"

msgctxt "machine_max_feedrate_y label"
msgid "Maximum Speed Y"
msgstr "Maximale Snelheid Y"

msgctxt "machine_max_feedrate_z label"
msgid "Maximum Speed Z"
msgstr "Maximale Snelheid Z"

msgctxt "support_tower_maximum_supported_diameter label"
msgid "Maximum Tower-Supported Diameter"
msgstr "Maximale pijler-ondersteunde diameter"

msgctxt "meshfix_maximum_travel_resolution label"
msgid "Maximum Travel Resolution"
msgstr "Maximale bewegingsresolutie"

msgctxt "machine_max_acceleration_x description"
msgid "Maximum acceleration for the motor of the X-direction"
msgstr "De maximale acceleratie van de motor in de X-richting"

msgctxt "machine_max_acceleration_y description"
msgid "Maximum acceleration for the motor of the Y-direction."
msgstr "De maximale acceleratie van de motor in de Y-richting."

msgctxt "machine_max_acceleration_z description"
msgid "Maximum acceleration for the motor of the Z-direction."
msgstr "De maximale acceleratie van de motor in de Z-richting."

msgctxt "machine_max_acceleration_e description"
msgid "Maximum acceleration for the motor of the filament."
msgstr "De maximale acceleratie van de motor van het filament."

msgctxt "bridge_sparse_infill_max_density description"
msgid "Maximum density of infill considered to be sparse. Skin over sparse infill is considered to be unsupported and so may be treated as a bridge skin."
msgstr "Maximale dichtheid van de vulling die als dun wordt beschouwd. Skin boven dunne vulling wordt als niet-ondersteund beschouwd en kan dus als een brugskin worden behandeld."

msgctxt "support_tower_maximum_supported_diameter description"
msgid "Maximum diameter in the X/Y directions of a small area which is to be supported by a specialized support tower."
msgstr "De maximale diameter in de X- en Y-richting van een kleiner gebied dat moet worden ondersteund door een speciale steunpijler."

msgctxt "max_extrusion_before_wipe description"
msgid "Maximum material that can be extruded before another nozzle wipe is initiated. If this value is less than the volume of material required in a layer, the setting has no effect in this layer, i.e. it is limited to one wipe per layer."
msgstr "Maximale materiaalhoeveelheid die kan worden geëxtrudeerd voordat de nozzle opnieuw wordt afgeveegd. Als deze waarde kleiner is dan het benodigde materiaalvolume in een laag, heeft de instelling geen effect op deze laag. Er wordt dan maar een keer per laag afgeveegd."

msgctxt "multiple_mesh_overlap label"
msgid "Merged Meshes Overlap"
msgstr "Samengevoegde rasters overlappen"

msgctxt "meshfix label"
msgid "Mesh Fixes"
msgstr "Modelcorrecties"

msgctxt "mesh_position_x label"
msgid "Mesh Position X"
msgstr "Rasterpositie X"

msgctxt "mesh_position_y label"
msgid "Mesh Position Y"
msgstr "Rasterpositie Y"

msgctxt "mesh_position_z label"
msgid "Mesh Position Z"
msgstr "Rasterpositie Z"

msgctxt "infill_mesh_order label"
msgid "Mesh Processing Rank"
msgstr "Rasterverwerkingsrang"

msgctxt "mesh_rotation_matrix label"
msgid "Mesh Rotation Matrix"
msgstr "Matrix rasterrotatie"

msgctxt "slicing_tolerance option middle"
msgid "Middle"
msgstr "Midden"

msgctxt "mold_width label"
msgid "Minimal Mold Width"
msgstr "Minimale matrijsbreedte"

msgctxt "machine_min_cool_heat_time_window label"
msgid "Minimal Time Standby Temperature"
msgstr "Minimale tijd stand-bytemperatuur"

msgctxt "bridge_wall_min_length label"
msgid "Minimum Bridge Wall Length"
msgstr "Minimale brugwandlengte"

msgctxt "min_even_wall_line_width label"
msgid "Minimum Even Wall Line Width"
msgstr "Minimum lijnbreedte even wand"

msgctxt "retraction_extrusion_window label"
msgid "Minimum Extrusion Distance Window"
msgstr "Minimaal Afstandsgebied voor Intrekken"

msgctxt "min_feature_size label"
msgid "Minimum Feature Size"
msgstr "Minimum elementgrootte"

msgctxt "machine_minimum_feedrate label"
msgid "Minimum Feedrate"
msgstr "Minimale Doorvoersnelheid"

msgctxt "support_tree_min_height_to_model label"
msgid "Minimum Height To Model"
msgstr "Minimale hoogte tot model"

msgctxt "min_infill_area label"
msgid "Minimum Infill Area"
msgstr "Minimumgebied vulling"

msgctxt "cool_min_layer_time label"
msgid "Minimum Layer Time"
msgstr "Minimale Laagtijd"

msgctxt "min_odd_wall_line_width label"
msgid "Minimum Odd Wall Line Width"
msgstr "Minimum breedte ongelijkmatige wandlijn"

msgctxt "minimum_polygon_circumference label"
msgid "Minimum Polygon Circumference"
msgstr "Minimale Polygoonomtrek"

msgctxt "min_skin_width_for_expansion label"
msgid "Minimum Skin Width for Expansion"
msgstr "Minimale skinbreedte voor uitbreiding"

msgctxt "cool_min_speed label"
msgid "Minimum Speed"
msgstr "Minimumsnelheid"

msgctxt "minimum_support_area label"
msgid "Minimum Support Area"
msgstr "Minimumgebied supportstructuur"

msgctxt "minimum_bottom_area label"
msgid "Minimum Support Floor Area"
msgstr "Minimumgebied supportvloer"

msgctxt "minimum_interface_area label"
msgid "Minimum Support Interface Area"
msgstr "Minimumgebied verbindingsstructuur"

msgctxt "minimum_roof_area label"
msgid "Minimum Support Roof Area"
msgstr "Minimumgebied supportdak"

msgctxt "support_xy_distance_overhang label"
msgid "Minimum Support X/Y Distance"
msgstr "Minimale X-/Y-afstand Supportstructuur"

msgctxt "min_bead_width label"
msgid "Minimum Thin Wall Line Width"
msgstr "Minimumbreedte dunne wandlijn"

msgctxt "coasting_min_volume label"
msgid "Minimum Volume Before Coasting"
msgstr "Minimaal Volume vóór Coasting"

msgctxt "min_wall_line_width label"
msgid "Minimum Wall Line Width"
msgstr "Minimumbreedte wandlijn"

msgctxt "minimum_interface_area description"
msgid "Minimum area size for support interface polygons. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Minimumgebied voor verbindingspolygonen. Polygonen met een gebied dat kleiner is dan deze waarde worden geprint als normale ondersteuning."

msgctxt "minimum_support_area description"
msgid "Minimum area size for support polygons. Polygons which have an area smaller than this value will not be generated."
msgstr "Minimumgebied voor steunpolygonen. Polygonen met een gebied dat kleiner is dan deze waarde, worden niet gegenereerd."

msgctxt "minimum_bottom_area description"
msgid "Minimum area size for the floors of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Minimumgebied voor de supportvloeren. Polygonen met een gebied dat kleiner is dan deze waarde worden geprint als normale ondersteuning."

msgctxt "minimum_roof_area description"
msgid "Minimum area size for the roofs of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Minimumgebied voor de supportdaken. Polygonen met een gebied dat kleiner is dan deze waarde worden geprint als normale ondersteuning."

msgctxt "min_feature_size description"
msgid "Minimum thickness of thin features. Model features that are thinner than this value will not be printed, while features thicker than the Minimum Feature Size will be widened to the Minimum Wall Line Width."
msgstr "Minimumdikte van dunne elementen. Modelelementen die dunner zijn dan deze waarde worden niet geprint, terwijl elementen die dikker zijn dan de minimale elementgrootte worden verbreed tot de minimale wandlijnbreedte."

msgctxt "support_conical_min_width description"
msgid "Minimum width to which the base of the conical support area is reduced. Small widths can lead to unstable support structures."
msgstr "Minimale breedte waarmee het grondvlak van het kegelvormige supportgebied wordt verkleind. Een geringe breedte kan leiden tot een instabiele supportstructuur."

msgctxt "mold_enabled label"
msgid "Mold"
msgstr "Matrijs"

msgctxt "mold_angle label"
msgid "Mold Angle"
msgstr "Matrijshoek"

msgctxt "mold_roof_height label"
msgid "Mold Roof Height"
msgstr "Dakhoogte matrijs"

msgctxt "ironing_monotonic label"
msgid "Monotonic Ironing Order"
msgstr "Monotone strijkvolgorde"

msgctxt "raft_surface_monotonic label"
msgid "Monotonic Raft Top Surface Order"
msgstr "Monotone volgorde van het bovenste oppervlak van het vlot"

msgctxt "roofing_monotonic label"
msgid "Monotonic Top Surface Order"
msgstr "Monotone volgorde bovenlaag"

msgctxt "skin_monotonic label"
msgid "Monotonic Top/Bottom Order"
msgstr "Monotone volgorde van boven naar beneden"

msgctxt "skirt_line_count description"
msgid "Multiple skirt lines help to prime your extrusion better for small models. Setting this to 0 will disable the skirt."
msgstr "Met meerdere skirtlijnen kunt u de doorvoer beter voorbereiden voor kleine modellen. Met de waarde 0 wordt de skirt uitgeschakeld."

msgctxt "initial_layer_line_width_factor description"
msgid "Multiplier of the line width on the first layer. Increasing this could improve bed adhesion."
msgstr "Vermenigvuldiging van de lijnbreedte van de eerste laag. Door deze te verhogen kan de hechting aan het bed worden verbeterd."

msgctxt "material_no_load_move_factor label"
msgid "No Load Move Factor"
msgstr "Verplaatsingsfactor zonder lading"

msgctxt "skin_no_small_gaps_heuristic label"
msgid "No Skin in Z Gaps"
msgstr "Geen skin in Z-gaten"

msgctxt "blackmagic description"
msgid "Non-traditional ways to print your models."
msgstr "Niet-traditionele manieren om uw modellen te printen."

msgctxt "adhesion_type option none"
msgid "None"
msgstr "Geen"

msgctxt "z_seam_corner option z_seam_corner_none"
msgid "None"
msgstr "Geen"

msgctxt "magic_mesh_surface_mode option normal"
msgid "Normal"
msgstr "Normaal"

msgctxt "prime_tower_mode option normal"
msgid "Normal"
msgstr "Normaal"

msgctxt "support_structure option normal"
msgid "Normal"
msgstr "Normaal"

msgctxt "meshfix_keep_open_polygons description"
msgid "Normally Cura tries to stitch up small holes in the mesh and remove parts of a layer with big holes. Enabling this option keeps those parts which cannot be stitched. This option should be used as a last resort option when everything else fails to produce proper g-code."
msgstr "Normaal probeert Cura kleine gaten in het raster te hechten en delen van een laag met grote gaten te verwijderen. Als u deze optie inschakelt, behoudt u de delen die niet kunnen worden gehecht. Deze optie kan als laatste redmiddel worden gebruikt als er geen andere manier meer is om correcte G-code te genereren."

msgctxt "retraction_combing option noskin"
msgid "Not in Skin"
msgstr "Niet in skin"

msgctxt "retraction_combing option no_outer_surfaces"
msgid "Not on Outer Surface"
msgstr "Niet op buitenzijde"

msgctxt "machine_nozzle_expansion_angle label"
msgid "Nozzle Angle"
msgstr "Nozzlehoek"

msgctxt "machine_nozzle_size label"
msgid "Nozzle Diameter"
msgstr "Nozzlediameter"

msgctxt "nozzle_disallowed_areas label"
msgid "Nozzle Disallowed Areas"
msgstr "Verboden gebieden voor de nozzle"

msgctxt "machine_nozzle_id label"
msgid "Nozzle ID"
msgstr "Nozzle-ID"

msgctxt "machine_nozzle_head_distance label"
msgid "Nozzle Length"
msgstr "Nozzlelengte"

msgctxt "switch_extruder_extra_prime_amount label"
msgid "Nozzle Switch Extra Prime Amount"
msgstr "Extra primehoeveelheid na wisselen van nozzle"

msgctxt "switch_extruder_prime_speed label"
msgid "Nozzle Switch Prime Speed"
msgstr "Primesnelheid bij Wisselen Nozzles"

msgctxt "switch_extruder_retraction_speed label"
msgid "Nozzle Switch Retract Speed"
msgstr "Intrekkingssnelheid bij Wisselen Nozzles"

msgctxt "switch_extruder_retraction_amount label"
msgid "Nozzle Switch Retraction Distance"
msgstr "Intrekafstand bij Wisselen Nozzles"

msgctxt "switch_extruder_retraction_speeds label"
msgid "Nozzle Switch Retraction Speed"
msgstr "Intreksnelheid bij Wisselen Nozzles"

msgctxt "machine_extruder_count label"
msgid "Number of Extruders"
msgstr "Aantal extruders"

msgctxt "extruders_enabled_count label"
msgid "Number of Extruders That Are Enabled"
msgstr "Aantal ingeschakelde extruders"

msgctxt "speed_slowdown_layers label"
msgid "Number of Slower Layers"
msgstr "Aantal Lagen met Lagere Printsnelheid"

msgctxt "extruders_enabled_count description"
msgid "Number of extruder trains that are enabled; automatically set in software"
msgstr "Het aantal extruder trains dat ingeschakeld is; automatisch ingesteld in de software"

msgctxt "machine_extruder_count description"
msgid "Number of extruder trains. An extruder train is the combination of a feeder, bowden tube, and nozzle."
msgstr "Aantal extruder trains. Een extruder train is de combinatie van een feeder, Bowden-buis en nozzle."

msgctxt "wipe_repeat_count description"
msgid "Number of times to move the nozzle across the brush."
msgstr "Aantal keren dat de nozzle over de borstel beweegt."

msgctxt "gradual_infill_steps description"
msgid "Number of times to reduce the infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Infill Density."
msgstr "Het aantal keren dat de vuldichtheid wordt gehalveerd naarmate er verder onder het oppervlak wordt geprint. Gebieden die zich dichter bij het oppervlak bevinden, krijgen een hogere dichtheid, tot de waarde die is opgegeven in de optie Dichtheid vulling."

msgctxt "gradual_support_infill_steps description"
msgid "Number of times to reduce the support infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Support Infill Density."
msgstr "Het aantal keren dat de dichtheid van de supportvulling wordt gehalveerd naarmate er verder onder het oppervlak wordt geprint. Gebieden die zich dichter bij het oppervlak bevinden, krijgen een hogere dichtheid, tot de waarde die is opgegeven in de optie Dichtheid supportvulling."

msgctxt "infill_pattern option tetrahedral"
msgid "Octet"
msgstr "Octet"

msgctxt "retraction_combing option off"
msgid "Off"
msgstr "Uit"

msgctxt "mesh_position_x description"
msgid "Offset applied to the object in the x direction."
msgstr "De offset die in de X-richting wordt toegepast op het object."

msgctxt "mesh_position_y description"
msgid "Offset applied to the object in the y direction."
msgstr "De offset die in de Y-richting wordt toegepast op het object."

msgctxt "mesh_position_z description"
msgid "Offset applied to the object in the z direction. With this you can perform what was used to be called 'Object Sink'."
msgstr "De offset die wordt toegepast op het object in de z-richting. Hiermee kunt u de taak uitvoeren die voorheen 'Object Sink' werd genoemd."

msgctxt "machine_use_extruder_offset_to_offset_coords label"
msgid "Offset with Extruder"
msgstr "Offset met extruder"

msgctxt "support_tree_rest_preference option buildplate"
msgid "On buildplate when possible"
msgstr "Op bouwplaat indien mogelijk"

msgctxt "support_tree_rest_preference option graceful"
msgid "On model if required"
msgstr "Op model indien nodig"

msgctxt "print_sequence option one_at_a_time"
msgid "One at a Time"
msgstr "Eén voor Eén"

msgctxt "retraction_hop_only_when_collides description"
msgid "Only perform a Z Hop when moving over printed parts which cannot be avoided by horizontal motion by Avoid Printed Parts when Traveling."
msgstr "Voer alleen een Z-sprong uit bij bewegingen over geprinte delen die niet kunnen worden vermeden met Geprinte Delen Mijden Tijdens Bewegingen."

msgctxt "ironing_only_highest_layer description"
msgid "Only perform ironing on the very last layer of the mesh. This saves time if the lower layers don't need a smooth surface finish."
msgstr "Strijk alleen de allerlaatste laag van het voorwerp. Dit bespaart tijd als de daaronder gelegen lagen geen glad oppervlak vereisen."

msgctxt "ooze_shield_angle label"
msgid "Ooze Shield Angle"
msgstr "Hoek Uitloopscherm"

msgctxt "ooze_shield_dist label"
msgid "Ooze Shield Distance"
msgstr "Afstand Uitloopscherm"

msgctxt "support_tree_branch_reach_limit label"
msgid "Optimal Branch Range"
msgstr "Optimaal vertakkingsbereik"

msgctxt "optimize_wall_printing_order label"
msgid "Optimize Wall Printing Order"
msgstr "Printvolgorde van wanden optimaliseren"

msgctxt "optimize_wall_printing_order description"
msgid "Optimize the order in which walls are printed so as to reduce the number of retractions and the distance travelled. Most parts will benefit from this being enabled but some may actually take longer so please compare the print time estimates with and without optimization. First layer is not optimized when choosing brim as build plate adhesion type."
msgstr "Optimaliseer de volgorde waarin wanden worden geprint om het aantal intrekbewegingen en de afgelegde afstand te verkleinen. Deze instelling is gunstig voor de meeste onderdelen. Bij sommige onderdelen duurt het printen echter langer. Controleer daarom de verwachte printtijd met en zonder optimalisatie. De eerste laag wordt niet geoptimaliseerd als u brim kiest als hechting aan platform."

msgctxt "machine_nozzle_tip_outer_diameter label"
msgid "Outer Nozzle Diameter"
msgstr "Buitendiameter nozzle"

msgctxt "acceleration_wall_0 label"
msgid "Outer Wall Acceleration"
msgstr "Buitenwandacceleratie"

msgctxt "wall_0_extruder_nr label"
msgid "Outer Wall Extruder"
msgstr "Extruder buitenwand"

msgctxt "wall_0_material_flow label"
msgid "Outer Wall Flow"
msgstr "Buitenste wanddoorvoer"

msgctxt "wall_0_inset label"
msgid "Outer Wall Inset"
msgstr "Uitsparing Buitenwand"

msgctxt "jerk_wall_0 label"
msgid "Outer Wall Jerk"
msgstr "Schok Buitenwand"

msgctxt "wall_line_width_0 label"
msgid "Outer Wall Line Width"
msgstr "Lijnbreedte Buitenwand"

msgctxt "speed_wall_0 label"
msgid "Outer Wall Speed"
msgstr "Snelheid Buitenwand"

msgctxt "wall_0_wipe_dist label"
msgid "Outer Wall Wipe Distance"
msgstr "Veegafstand buitenwand"

msgctxt "group_outer_walls description"
msgid "Outer walls of different islands in the same layer are printed in sequence. When enabled the amount of flow changes is limited because walls are printed one type at a time, when disabled the number of travels between islands is reduced because walls in the same islands are grouped."
msgstr "Buitenwanden van verschillende eilanden in dezelfde laag worden achtereenvolgens geprint. Wanneer ingeschakeld, wordt de hoeveelheid stroomveranderingen beperkt omdat wanden één type tegelijk worden geprint. Wanneer uitgeschakeld, wordt het aantal verplaatsingen tussen eilanden verminderd omdat wanden op dezelfde eilanden worden gegroepeerd."

msgctxt "brim_location option outside"
msgid "Outside Only"
msgstr "Alleen buitenkant"

msgctxt "inset_direction option outside_in"
msgid "Outside To Inside"
msgstr "Van buiten naar binnen"

msgctxt "wall_overhang_angle label"
msgid "Overhanging Wall Angle"
msgstr "Hoek Overhangende Wand"

msgctxt "wall_overhang_speed_factor label"
msgid "Overhanging Wall Speed"
msgstr "Snelheid Overhangende Wand"

msgctxt "wall_overhang_speed_factor description"
msgid "Overhanging walls will be printed at this percentage of their normal print speed."
msgstr "Overhangende wanden worden geprint met een snelheid die gelijk is aan dit percentage van hun normale printsnelheid."

msgctxt "wipe_pause description"
msgid "Pause after the unretract."
msgstr "Pauzeren na het ongedaan maken van intrekken."

msgctxt "bridge_fan_speed description"
msgid "Percentage fan speed to use when printing bridge walls and skin."
msgstr "Percentage ventilatorsnelheid tijdens het printen van brugwanden en -skin."

msgctxt "bridge_fan_speed_2 description"
msgid "Percentage fan speed to use when printing the second bridge skin layer."
msgstr "Percentage ventilatorsnelheid tijdens het printen van de tweede brugskinlaag."

msgctxt "support_supported_skin_fan_speed description"
msgid "Percentage fan speed to use when printing the skin regions immediately above the support. Using a high fan speed can make the support easier to remove."
msgstr "Percentage van de ventilatorsnelheid dat tijdens het printen van skinregio's direct boven de supportstructuur moet worden gebruikt. Bij gebruikmaking van een hoge ventilatorsnelheid kan de supportstructuur gemakkelijker worden verwijderd."

msgctxt "bridge_fan_speed_3 description"
msgid "Percentage fan speed to use when printing the third bridge skin layer."
msgstr "Percentage ventilatorsnelheid tijdens het printen van de derde brugskinlaag."

msgctxt "minimum_polygon_circumference description"
msgid "Polygons in sliced layers that have a circumference smaller than this amount will be filtered out. Lower values lead to higher resolution mesh at the cost of slicing time. It is meant mostly for high resolution SLA printers and very tiny 3D models with a lot of details."
msgstr "Polygonen in geslicete lagen, die een kleinere omtrek hebben dan deze waarde, worden eruit gefilterd. Bij lagere waarden krijgt het raster een hogere resolutie, waardoor het slicen langer duurt. Dit is voornamelijk bedoeld voor SLA-printers met een hoge resolutie en zeer kleine 3D-modellen die veel details bevatten."

msgctxt "support_tree_angle_slow label"
msgid "Preferred Branch Angle"
msgstr "Geprefereerde vertakkingshoek"

msgctxt "wall_transition_filter_deviation description"
msgid "Prevent transitioning back and forth between one extra wall and one less. This margin extends the range of line widths which follow to [Minimum Wall Line Width - Margin, 2 * Minimum Wall Line Width + Margin]. Increasing this margin reduces the number of transitions, which reduces the number of extrusion starts/stops and travel time. However, large line width variation can lead to under- or overextrusion problems."
msgstr "Voorkom herhaaldelijke overgangen tussen een wand meer en een wand minder. Deze marge vergroot het aantal lijnbreedtes dat volgt op [minimumbreedte wandlijn - marge, 2 * minimumbreedte wandlijn + marge]. Door de marge te vergroten reduceert u het aantal overgangen, wat weer het aantal doorvoerstarts/-stops en de tijd van de beweging reduceert. Een grote variatie in lijnbreedtes kan echter wel leiden tot problemen met te geringe of te hoge extrusie."

msgctxt "acceleration_prime_tower label"
msgid "Prime Tower Acceleration"
msgstr "Acceleratie Primepijler"

msgctxt "prime_tower_brim_enable label"
msgid "Prime Tower Base"
msgstr "Basis van de Primepijler"

msgctxt "prime_tower_base_height label"
msgid "Prime Tower Base Height"
msgstr "Hoogte van de basis van de Primepijler"

msgctxt "prime_tower_base_size label"
msgid "Prime Tower Base Size"
msgstr "Grootte van de basis van de Primepijler"

msgctxt "prime_tower_base_curve_magnitude label"
msgid "Prime Tower Base Slope"
msgstr "Basis hellingshoek van de Prime Toren"

msgctxt "prime_tower_flow label"
msgid "Prime Tower Flow"
msgstr "Doorvoer Primepijler"

msgctxt "jerk_prime_tower label"
msgid "Prime Tower Jerk"
msgstr "Schok Primepijler"

msgctxt "prime_tower_line_width label"
msgid "Prime Tower Line Width"
msgstr "Lijnbreedte Primepijler"

msgctxt "prime_tower_max_bridging_distance label"
msgid "Prime Tower Maximum Bridging Distance"
msgstr "Maximale overbruggingsafstand voorbereidingstoren"

msgctxt "prime_tower_min_volume label"
msgid "Prime Tower Minimum Volume"
msgstr "Minimumvolume primepijler"

msgctxt "prime_tower_raft_base_line_spacing label"
msgid "Prime Tower Raft Line Spacing"
msgstr "Lijnafstand van het vlot van de Primepijler"

msgctxt "prime_tower_size label"
msgid "Prime Tower Size"
msgstr "Formaat Primepijler"

msgctxt "speed_prime_tower label"
msgid "Prime Tower Speed"
msgstr "Snelheid Primepijler"

msgctxt "prime_tower_mode label"
msgid "Prime Tower Type"
msgstr "Type voorbereidingstoren"

msgctxt "prime_tower_position_x label"
msgid "Prime Tower X Position"
msgstr "X-positie Primepijler"

msgctxt "prime_tower_position_y label"
msgid "Prime Tower Y Position"
msgstr "Y-positie Primepijler"

msgctxt "acceleration_print label"
msgid "Print Acceleration"
msgstr "Printacceleratie"

msgctxt "jerk_print label"
msgid "Print Jerk"
msgstr "Printschok"

msgctxt "ppr label"
msgid "Print Process Reporting"
msgstr "Rapportage van printproces"

msgctxt "print_sequence label"
msgid "Print Sequence"
msgstr "Printvolgorde"

msgctxt "speed_print label"
msgid "Print Speed"
msgstr "Printsnelheid"

msgctxt "fill_outline_gaps label"
msgid "Print Thin Walls"
msgstr "Dunne wanden printen"

msgctxt "brim_location description"
msgid "Print a brim on the outside of the model, inside, or both. Depending on the model, this helps reducing the amount of brim you need to remove afterwards, while ensuring a proper bed adhesion."
msgstr "Print een rand aan de buitenkant van het model, aan de binnenkant of aan beide kanten. Afhankelijk van het model helpt dit om de hoeveelheid rand die u achteraf moet verwijderen te verminderen, terwijl het voor een goede hechting aan het bed zorgt."

msgctxt "prime_tower_enable description"
msgid "Print a tower next to the print which serves to prime the material after each nozzle switch."
msgstr "Print een pijler naast de print, waarop het materiaal na iedere nozzlewisseling wordt ingespoeld."

msgctxt "infill_support_enabled description"
msgid "Print infill structures only where tops of the model should be supported. Enabling this reduces print time and material usage, but leads to ununiform object strength."
msgstr "Print alleen vulstructuren waarvan de bovenkant van het model moet worden ondersteund. Hiermee reduceert u de printtijd en het materiaalgebruik. Dit kan echter leiden tot een niet gelijkmatige objectsterkte."

msgctxt "ironing_monotonic description"
msgid "Print ironing lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Print strijklijnen in een volgorde die ervoor zorgt dat ze altijd in één richting overlappen met aangrenzende lijnen. Hierdoor duurt het iets langer om te printen, maar platte oppervlakken zien er dan consistenter uit."

msgctxt "mold_enabled description"
msgid "Print models as a mold, which can be cast in order to get a model which resembles the models on the build plate."
msgstr "Print modellen als matrijs, die vervolgens kan worden gegoten om een model te krijgen dat lijkt op de modellen op het platform."

msgctxt "fill_outline_gaps description"
msgid "Print pieces of the model which are horizontally thinner than the nozzle size."
msgstr "Print delen van het model die horizontaal dunner zijn dan de maat van de nozzle."

msgctxt "raft_surface_monotonic description"
msgid "Print raft top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes the surface look more consistent, which is also visible on the model bottom surface."
msgstr "Print de lijnen van het bovenste oppervlak van het vlot in een volgorde die ervoor zorgt dat ze altijd overlappen met aangrenzende lijnen in één richting. Dit kost iets meer tijd om te printen, maar het oppervlak ziet er consistenter uit, wat ook zichtbaar is op de onderkant van het model."

msgctxt "bridge_skin_speed_2 description"
msgid "Print speed to use when printing the second bridge skin layer."
msgstr "Printsnelheid tijdens het printen van de tweede brugskinlaag."

msgctxt "bridge_skin_speed_3 description"
msgid "Print speed to use when printing the third bridge skin layer."
msgstr "Printsnelheid tijdens het printen van de derde brugskinlaag."

msgctxt "print_temp_anomaly_limit label"
msgid "Print temperature Limit"
msgstr "Limiet printtemperatuur"

msgctxt "print_temp_warn_limit label"
msgid "Print temperature Warning"
msgstr "Waarschuwing printtemperatuur"

msgctxt "infill_before_walls description"
msgid "Print the infill before printing the walls. Printing the walls first may lead to more accurate walls, but overhangs print worse. Printing the infill first leads to sturdier walls, but the infill pattern might sometimes show through the surface."
msgstr "Print de vulling voordat de wanden worden geprint. Wanneer u eerst de wanden print, worden deze nauwkeuriger geprint, maar wordt de overhang mogelijk van mindere kwaliteit. Wanneer u eerst de vulling print, worden de wanden steviger, maar schijnt het vulpatroon mogelijk door."

msgctxt "roofing_monotonic description"
msgid "Print top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Print de lijnen van de bovenlaag in een volgorde die ervoor zorgt dat ze altijd in één richting overlappen met aangrenzende lijnen. Hierdoor duurt het iets langer om te printen, maar platte oppervlakken zien er dan consistenter uit."

msgctxt "skin_monotonic description"
msgid "Print top/bottom lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Print boven- en onderlijnen in een volgorde die ervoor zorgt dat ze altijd in één richting overlappen met aangrenzende lijnen. Hierdoor duurt het iets langer om te printen, maar platte oppervlakken zien er dan consistenter uit."

msgctxt "material_print_temperature label"
msgid "Printing Temperature"
msgstr "Printtemperatuur"

msgctxt "material_print_temperature_layer_0 label"
msgid "Printing Temperature Initial Layer"
msgstr "Printtemperatuur van de eerste laag"

msgctxt "skirt_height description"
msgid "Printing the innermost skirt line with multiple layers makes it easy to remove the skirt."
msgstr "Het printen van de binnenste skirt-lijn met meerdere lagen maakt het gemakkelijk om de skirt te verwijderen."

msgctxt "alternate_extra_perimeter description"
msgid "Prints an extra wall at every other layer. This way infill gets caught between these extra walls, resulting in stronger prints."
msgstr "Print op afwisselende lagen een extra wand. Op deze manier wordt vulling tussen deze extra wanden gevangen, wat leidt tot sterkere prints."

msgctxt "resolution label"
msgid "Quality"
msgstr "Kwaliteit"

msgctxt "infill_pattern option quarter_cubic"
msgid "Quarter Cubic"
msgstr "Afgeknotte kubus"

msgctxt "adhesion_type option raft"
msgid "Raft"
msgstr "Raft"

msgctxt "raft_airgap label"
msgid "Raft Air Gap"
msgstr "Luchtruimte Raft"

msgctxt "raft_base_margin label"
msgid "Raft Base Extra Margin"
msgstr "Extra marge voor vlot op de basis"

msgctxt "raft_base_extruder_nr label"
msgid "Raft Base Extruder"
msgstr "Raft basisextruder"

msgctxt "raft_base_fan_speed label"
msgid "Raft Base Fan Speed"
msgstr "Ventilatorsnelheid Grondlaag Raft"

msgctxt "raft_base_line_spacing label"
msgid "Raft Base Line Spacing"
msgstr "Tussenruimte Lijnen Grondvlak Raft"

msgctxt "raft_base_line_width label"
msgid "Raft Base Line Width"
msgstr "Lijnbreedte Grondvlak Raft"

msgctxt "raft_base_acceleration label"
msgid "Raft Base Print Acceleration"
msgstr "Printacceleratie Grondvlak Raft"

msgctxt "raft_base_jerk label"
msgid "Raft Base Print Jerk"
msgstr "Printschok Grondvlak Raft"

msgctxt "raft_base_speed label"
msgid "Raft Base Print Speed"
msgstr "Printsnelheid Grondvlak Raft"

msgctxt "raft_base_smoothing label"
msgid "Raft Base Smoothing"
msgstr "Vlotbasis gladmaken"

msgctxt "raft_base_thickness label"
msgid "Raft Base Thickness"
msgstr "Dikte Grondvlak Raft"

msgctxt "raft_base_wall_count label"
msgid "Raft Base Wall Count"
msgstr "Aantal wanden grondvlak raft"

msgctxt "raft_margin label"
msgid "Raft Extra Margin"
msgstr "Extra Marge Raft"

msgctxt "raft_fan_speed label"
msgid "Raft Fan Speed"
msgstr "Ventilatorsnelheid Raft"

msgctxt "raft_interface_margin label"
msgid "Raft Middle Extra Margin"
msgstr "Extra marge voor vlot in het midden"

msgctxt "raft_interface_extruder_nr label"
msgid "Raft Middle Extruder"
msgstr "Raft middelste extruder"

msgctxt "raft_interface_fan_speed label"
msgid "Raft Middle Fan Speed"
msgstr "Ventilatorsnelheid Midden Raft"

msgctxt "raft_interface_layers label"
msgid "Raft Middle Layers"
msgstr "Raft middelste lagen"

msgctxt "raft_interface_line_width label"
msgid "Raft Middle Line Width"
msgstr "Lijnbreedte Midden Raft"

msgctxt "raft_interface_acceleration label"
msgid "Raft Middle Print Acceleration"
msgstr "Printacceleratie Midden Raft"

msgctxt "raft_interface_jerk label"
msgid "Raft Middle Print Jerk"
msgstr "Printschok Midden Raft"

msgctxt "raft_interface_speed label"
msgid "Raft Middle Print Speed"
msgstr "Printsnelheid Midden Raft"

msgctxt "raft_interface_smoothing label"
msgid "Raft Middle Smoothing"
msgstr "Midden van vlot gladmaken"

msgctxt "raft_interface_line_spacing label"
msgid "Raft Middle Spacing"
msgstr "Tussenruimte Midden Raft"

msgctxt "raft_interface_thickness label"
msgid "Raft Middle Thickness"
msgstr "Lijndikte Midden Raft"

msgctxt "raft_interface_wall_count label"
msgid "Raft Middle Wall Count"
msgstr "Aantal middenwanden vlot"

msgctxt "raft_acceleration label"
msgid "Raft Print Acceleration"
msgstr "Printacceleratie Raft"

msgctxt "raft_jerk label"
msgid "Raft Print Jerk"
msgstr "Printschok Raft"

msgctxt "raft_speed label"
msgid "Raft Print Speed"
msgstr "Printsnelheid Raft"

msgctxt "raft_smoothing label"
msgid "Raft Smoothing"
msgstr "Raft effenen"

msgctxt "raft_surface_margin label"
msgid "Raft Top Extra Margin"
msgstr "Extra marge voor vlot aan bovenkant"

msgctxt "raft_surface_extruder_nr label"
msgid "Raft Top Extruder"
msgstr "Raft bovenste extruder"

msgctxt "raft_surface_fan_speed label"
msgid "Raft Top Fan Speed"
msgstr "Ventilatorsnelheid Bovenkant Raft"

msgctxt "raft_surface_thickness label"
msgid "Raft Top Layer Thickness"
msgstr "Dikte Bovenlaag Raft"

msgctxt "raft_surface_layers label"
msgid "Raft Top Layers"
msgstr "Bovenlagen Raft"

msgctxt "raft_surface_line_width label"
msgid "Raft Top Line Width"
msgstr "Breedte Bovenste Lijn Raft"

msgctxt "raft_surface_acceleration label"
msgid "Raft Top Print Acceleration"
msgstr "Printacceleratie Bovenkant Raft"

msgctxt "raft_surface_jerk label"
msgid "Raft Top Print Jerk"
msgstr "Printschok Bovenkant Raft"

msgctxt "raft_surface_speed label"
msgid "Raft Top Print Speed"
msgstr "Printsnelheid Bovenkant Raft"

msgctxt "raft_surface_smoothing label"
msgid "Raft Top Smoothing"
msgstr "Bovenkant van vlot gladmaken"

msgctxt "raft_surface_line_spacing label"
msgid "Raft Top Spacing"
msgstr "Bovenruimte Raft"

msgctxt "raft_surface_wall_count label"
msgid "Raft Top Wall Count"
msgstr "Aantal bovenwanden vlot"

msgctxt "raft_wall_count label"
msgid "Raft Wall Count"
msgstr "Aantal vlotwanden"

msgctxt "z_seam_type option random"
msgid "Random"
msgstr "Willekeurig"

msgctxt "infill_randomize_start_location label"
msgid "Randomize Infill Start"
msgstr "Start willekeurig invullen"

msgctxt "infill_randomize_start_location description"
msgid "Randomize which infill line is printed first. This prevents one segment becoming the strongest, but it does so at the cost of an additional travel move."
msgstr "Bepaal willekeurig welke invullijn het eerst wordt geprint. Dit voorkomt dat één segment het sterkst wordt, maar gaat ten koste van een extra beweging."

msgctxt "magic_fuzzy_skin_enabled description"
msgid "Randomly jitter while printing the outer wall, so that the surface has a rough and fuzzy look."
msgstr "Door willekeurig trillen tijdens het printen van de buitenwand wordt het oppervlak hiervan ruw en ongelijk."

msgctxt "machine_shape option rectangular"
msgid "Rectangular"
msgstr "Rechthoekig"

msgctxt "cool_fan_speed_min label"
msgid "Regular Fan Speed"
msgstr "Normale Ventilatorsnelheid"

msgctxt "cool_fan_full_at_height label"
msgid "Regular Fan Speed at Height"
msgstr "Normale Ventilatorsnelheid op Hoogte"

msgctxt "cool_fan_full_layer label"
msgid "Regular Fan Speed at Layer"
msgstr "Normale Ventilatorsnelheid op Laag"

msgctxt "cool_min_layer_time_fan_speed_max label"
msgid "Regular/Maximum Fan Speed Threshold"
msgstr "Drempelwaarde Normale/Maximale Ventilatorsnelheid"

msgctxt "relative_extrusion label"
msgid "Relative Extrusion"
msgstr "Relatieve Extrusie"

msgctxt "meshfix_union_all_remove_holes label"
msgid "Remove All Holes"
msgstr "Alle Gaten Verwijderen"

msgctxt "remove_empty_first_layers label"
msgid "Remove Empty First Layers"
msgstr "Lege eerste lagen verwijderen"

msgctxt "carve_multiple_volumes label"
msgid "Remove Mesh Intersection"
msgstr "Rastersnijpunt verwijderen"

msgctxt "raft_base_remove_inside_corners label"
msgid "Remove Raft Base Inside Corners"
msgstr "Binnenhoeken van basis van vlot verwijderen"

msgctxt "raft_remove_inside_corners label"
msgid "Remove Raft Inside Corners"
msgstr "Binnenhoeken raft verwijderen"

msgctxt "raft_interface_remove_inside_corners label"
msgid "Remove Raft Middle Inside Corners"
msgstr "Binnenhoeken van vlot in het midden verwijderen"

msgctxt "raft_surface_remove_inside_corners label"
msgid "Remove Raft Top Inside Corners"
msgstr "Binnenhoeken van vlot aan de bovenkant verwijderen"

msgctxt "carve_multiple_volumes description"
msgid "Remove areas where multiple meshes are overlapping with each other. This may be used if merged dual material objects overlap with each other."
msgstr "Hiermee verwijdert u gebieden waar meerdere rasters elkaar overlappen. Deze functie kan worden gebruikt als samengevoegde objecten van twee materialen elkaar overlappen."

msgctxt "remove_empty_first_layers description"
msgid "Remove empty layers beneath the first printed layer if they are present. Disabling this setting can cause empty first layers if the Slicing Tolerance setting is set to Exclusive or Middle."
msgstr "Hiermee worden de lege lagen onder de eerste geprinte laag verwijderd, indien aanwezig. Als u deze instelling uitschakelt, kunnen lege eerste lagen ontstaan als de Slicetolerantie is ingesteld op Exclusief of Midden."

msgctxt "raft_base_remove_inside_corners description"
msgid "Remove inside corners from the raft base, causing the raft to become convex."
msgstr "Verwijder de binnenhoeken van de basis van het vlot, waardoor het vlot bol wordt."

msgctxt "raft_interface_remove_inside_corners description"
msgid "Remove inside corners from the raft middle part, causing the raft to become convex."
msgstr "Verwijder de binnenhoeken van het middelste deel van het vlot, waardoor het vlot bol wordt."

msgctxt "raft_surface_remove_inside_corners description"
msgid "Remove inside corners from the raft top part, causing the raft to become convex."
msgstr "Verwijder de binnenhoeken van het bovenste deel van het vlot, waardoor het vlot bol wordt."

msgctxt "raft_remove_inside_corners description"
msgid "Remove inside corners from the raft, causing the raft to become convex."
msgstr "Verwijdering van de binnenhoeken van de raft maakt de raft bol."

msgctxt "meshfix_union_all_remove_holes description"
msgid "Remove the holes in each layer and keep only the outside shape. This will ignore any invisible internal geometry. However, it also ignores layer holes which can be viewed from above or below."
msgstr "Verwijder de gaten in elke laag en behoudt u alleen de buitenvorm. Hiermee negeert u eventuele onzichtbare interne geometrie. U negeert echter ook gaten in lagen die u van boven- of onderaf kunt zien."

msgctxt "machine_gcode_flavor option RepRap (RepRap)"
msgid "RepRap"
msgstr "RepRap"

msgctxt "machine_gcode_flavor option Repetier"
msgid "Repetier"
msgstr "Repetier"

msgctxt "skin_outline_count description"
msgid "Replaces the outermost part of the top/bottom pattern with a number of concentric lines. Using one or two lines improves roofs that start on infill material."
msgstr "Vervang het buitenste gedeelte van het patroon boven-/onderkant door een aantal concentrische lijnen. Het gebruik van 1 of 2 lijnen verbetert daken die op vulmateriaal beginnen."

msgctxt "ppr description"
msgid "Reporting events that go out of set thresholds"
msgstr "Rapportagegebeurtenissen die ingestelde drempels overschrijden"

msgctxt "support_tree_rest_preference label"
msgid "Rest Preference"
msgstr "Plaatsings voorkeur"

msgctxt "travel_retract_before_outer_wall label"
msgid "Retract Before Outer Wall"
msgstr "Intrekken voor buitenwand"

msgctxt "retract_at_layer_change label"
msgid "Retract at Layer Change"
msgstr "Intrekken bij laagwisseling"

msgctxt "retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "Hiermee wordt het filament ingetrokken wanneer de nozzle over een niet-printbaar gebied gaat."

msgctxt "wipe_retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "Hiermee wordt het filament ingetrokken wanneer de nozzle over een niet-printbaar gebied gaat."

msgctxt "retract_at_layer_change description"
msgid "Retract the filament when the nozzle is moving to the next layer."
msgstr "Trek het filament in wanneer de nozzle naar de volgende laag beweegt."

msgctxt "retraction_amount label"
msgid "Retraction Distance"
msgstr "Intrekafstand"

msgctxt "retraction_extra_prime_amount label"
msgid "Retraction Extra Prime Amount"
msgstr "Extra Primehoeveelheid na Intrekken"

msgctxt "retraction_min_travel label"
msgid "Retraction Minimum Travel"
msgstr "Minimale Afstand voor Intrekken"

msgctxt "retraction_prime_speed label"
msgid "Retraction Prime Speed"
msgstr "Intreksnelheid (Primen)"

msgctxt "retraction_retract_speed label"
msgid "Retraction Retract Speed"
msgstr "Intreksnelheid (Intrekken)"

msgctxt "retraction_speed label"
msgid "Retraction Speed"
msgstr "Intreksnelheid"

msgctxt "z_seam_position option right"
msgid "Right"
msgstr "Rechts"

msgctxt "machine_scale_fan_speed_zero_to_one label"
msgid "Scale Fan Speed To 0-1"
msgstr "Zet de ventilatorsnelheid op 0-1"

msgctxt "machine_scale_fan_speed_zero_to_one description"
msgid "Scale the fan speed to be between 0 and 1 instead of between 0 and 256."
msgstr "Zet de ventilatorsnelheid op een waarde tussen 0 en 1 in plaats van tussen 0 en 256."

msgctxt "material_shrinkage_percentage label"
msgid "Scaling Factor Shrinkage Compensation"
msgstr "Schaalfactor krimpcompensatie"

msgctxt "support_meshes_present label"
msgid "Scene Has Support Meshes"
msgstr "Scène heeft supportrasters"

msgctxt "z_seam_corner label"
msgid "Seam Corner Preference"
msgstr "Voorkeur van naad en hoek"

msgctxt "user_defined_print_order_enabled label"
msgid "Set Print Sequence Manually"
msgstr "Handmatig afdrukvolgorde instellen"

msgctxt "draft_shield_height_limitation description"
msgid "Set the height of the draft shield. Choose to print the draft shield at the full height of the model or at a limited height."
msgstr "Stel de hoogte van het tochtscherm in. U kunt ervoor kiezen een tochtscherm met dezelfde hoogte als het model of lager te printen."

msgctxt "dual description"
msgid "Settings used for printing with multiple extruders."
msgstr "Instellingen die worden gebruikt voor het printen met meerdere extruders."

msgctxt "command_line_settings description"
msgid "Settings which are only used if CuraEngine isn't called from the Cura frontend."
msgstr "Instellingen die alleen worden gebruikt als CuraEngine niet wordt aangeroepen door de Cura-frontend."

msgctxt "machine_extruders_shared_nozzle_initial_retraction label"
msgid "Shared Nozzle Initial Retraction"
msgstr "Initiële terugtrekking gedeelde nozzle"

msgctxt "z_seam_type option sharpest_corner"
msgid "Sharpest Corner"
msgstr "Scherpste hoek"

msgctxt "shell description"
msgid "Shell"
msgstr "Shell"

msgctxt "z_seam_type option shortest"
msgid "Shortest"
msgstr "Kortste"

msgctxt "machine_show_variants label"
msgid "Show Machine Variants"
msgstr "Machinevarianten tonen"

msgctxt "skin_edge_support_layers label"
msgid "Skin Edge Support Layers"
msgstr "Lagen skinrandondersteuning"

msgctxt "skin_edge_support_thickness label"
msgid "Skin Edge Support Thickness"
msgstr "Dikte skinrandondersteuning"

msgctxt "expand_skins_expand_distance label"
msgid "Skin Expand Distance"
msgstr "Uitbreidingsafstand van skin"

msgctxt "skin_overlap_mm label"
msgid "Skin Overlap"
msgstr "Overlap Skin"

msgctxt "skin_overlap label"
msgid "Skin Overlap Percentage"
msgstr "Overlappercentage Skin"

msgctxt "skin_preshrink label"
msgid "Skin Removal Width"
msgstr "Verwijderingsbreedte skin"

msgctxt "min_skin_width_for_expansion description"
msgid "Skin areas narrower than this are not expanded. This avoids expanding the narrow skin areas that are created when the model surface has a slope close to the vertical."
msgstr "Skingebieden die smaller zijn dan deze waarde, worden niet uitgebreid. Dit voorkomt het uitbreiden van smalle skingebieden die worden gemaakt wanneer het modeloppervlak een nagenoeg verticale helling heeft."

msgctxt "support_zag_skip_count description"
msgid "Skip one in every N connection lines to make the support structure easier to break away."
msgstr "Sla elke N verbindingslijnen één lijn over zodat de supportstructuur gemakkelijker kan worden weggebroken."

msgctxt "support_skip_some_zags description"
msgid "Skip some support line connections to make the support structure easier to break away. This setting is applicable to the Zig Zag support infill pattern."
msgstr "Sla enkele verbindingen tussen lijnen van de supportstructuur over zodat deze gemakkelijker kan worden weggebroken. Deze instelling is van toepassing op het zigzag-vulpatroon van de supportstructuur."

msgctxt "adhesion_type option skirt"
msgid "Skirt"
msgstr "Skirt"

msgctxt "skirt_gap label"
msgid "Skirt Distance"
msgstr "Skirtafstand"

msgctxt "skirt_height label"
msgid "Skirt Height"
msgstr "Hoogte Skirt"

msgctxt "skirt_line_count label"
msgid "Skirt Line Count"
msgstr "Aantal Skirtlijnen"

msgctxt "acceleration_skirt_brim label"
msgid "Skirt/Brim Acceleration"
msgstr "Acceleratie Skirt/Brim"

msgctxt "skirt_brim_extruder_nr label"
msgid "Skirt/Brim Extruder"
msgstr "Extruder Skirt/Brim"

msgctxt "skirt_brim_material_flow label"
msgid "Skirt/Brim Flow"
msgstr "Doorvoer skirt/brim"

msgctxt "jerk_skirt_brim label"
msgid "Skirt/Brim Jerk"
msgstr "Schok Skirt/Brim"

msgctxt "skirt_brim_line_width label"
msgid "Skirt/Brim Line Width"
msgstr "Lijnbreedte Skirt/Brim"

msgctxt "skirt_brim_minimal_length label"
msgid "Skirt/Brim Minimum Length"
msgstr "Minimale Skirt-/Brimlengte"

msgctxt "skirt_brim_speed label"
msgid "Skirt/Brim Speed"
msgstr "Skirt-/Brimsnelheid"

msgctxt "slicing_tolerance label"
msgid "Slicing Tolerance"
msgstr "Slicetolerantie"

msgctxt "small_feature_speed_factor_0 label"
msgid "Small Feature Initial Layer Speed"
msgstr "Kleine kenmerken eerste laagsnelheid"

msgctxt "small_feature_max_length label"
msgid "Small Feature Max Length"
msgstr "Maximale lengte klein kenmerk"

msgctxt "small_feature_speed_factor label"
msgid "Small Feature Speed"
msgstr "Klein kenmerksnelheid"

msgctxt "small_hole_max_size label"
msgid "Small Hole Max Size"
msgstr "Maximale grootte kleine gaten"

msgctxt "cool_min_temperature label"
msgid "Small Layer Printing Temperature"
msgstr "Printtemperatuur van de kleine laag"

msgctxt "small_skin_on_surface label"
msgid "Small Top/Bottom On Surface"
msgstr "Kleine bovenkant/onderkant op oppervlak"

msgctxt "small_skin_width label"
msgid "Small Top/Bottom Width"
msgstr "Kleine breedte boven/onderzijde"

msgctxt "small_feature_speed_factor_0 description"
msgid "Small features on the first layer will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "Kleine kenmerken op de eerste laag worden geprint met een snelheid die gelijk is aan dit percentage van hun normale printsnelheid. Langzamer printen kan de hechting en nauwkeurigheid verbeteren."

msgctxt "small_feature_speed_factor description"
msgid "Small features will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "Kleine kernmerken worden geprint met een snelheid die gelijk is aan dit percentage van hun normale printsnelheid. Langzamer printen kan de hechting en nauwkeurigheid verbeteren."

msgctxt "small_skin_width description"
msgid "Small top/bottom regions are filled with walls instead of the default top/bottom pattern. This helps to avoids jerky motions. Off for the topmost (air-exposed) layer by default (see 'Small Top/Bottom On Surface')."
msgstr "Kleine boven-/ondergebieden worden gevuld met muren in plaats van het standaard boven-/onderpatroon. Dit helpt om schokkerige bewegingen te voorkomen. Standaard uit voor de bovenste (aan lucht blootgestelde) laag (zie 'Kleine boven-/onderkant op oppervlak')."

msgctxt "brim_smart_ordering label"
msgid "Smart Brim"
msgstr "Slimme Brim"

msgctxt "z_seam_corner option z_seam_corner_weighted"
msgid "Smart Hiding"
msgstr "Slim verbergen"

msgctxt "smooth_spiralized_contours label"
msgid "Smooth Spiralized Contours"
msgstr "Gespiraliseerde contouren effenen"

msgctxt "smooth_spiralized_contours description"
msgid "Smooth the spiralized contours to reduce the visibility of the Z seam (the Z seam should be barely visible on the print but will still be visible in the layer view). Note that smoothing will tend to blur fine surface details."
msgstr "Maak de gespiraliseerde contouren vlak om de zichtbaarheid van de Z-naad te verminderen (de Z-naad mag in de print nauwelijks zichtbaar zijn, maar is nog wel zichtbaar in de laagweergave). Houd er rekening mee dat fijne oppervlaktedetails worden vervaagd door het effenen."

msgctxt "retraction_extra_prime_amount description"
msgid "Some material can ooze away during a travel move, which can be compensated for here."
msgstr "Tijdens een beweging kan materiaal verloren gaan, wat met deze optie kan worden gecompenseerd."

msgctxt "wipe_retraction_extra_prime_amount description"
msgid "Some material can ooze away during a wipe travel moves, which can be compensated for here."
msgstr "Tijdens veegbewegingen kan materiaal verloren gaan, wat met deze optie kan worden gecompenseerd."

msgctxt "blackmagic label"
msgid "Special Modes"
msgstr "Speciale Modi"

msgctxt "speed description"
msgid "Speed"
msgstr "Snelheid"

msgctxt "speed label"
msgid "Speed"
msgstr "Snelheid"

msgctxt "wipe_hop_speed description"
msgid "Speed to move the z-axis during the hop."
msgstr "Snelheid waarmee de Z-as wordt verplaatst tijdens de sprong."

msgctxt "magic_spiralize label"
msgid "Spiralize Outer Contour"
msgstr "Buitencontour Spiraliseren"

msgctxt "magic_spiralize description"
msgid "Spiralize smooths out the Z move of the outer edge. This will create a steady Z increase over the whole print. This feature turns a solid model into a single walled print with a solid bottom. This feature should only be enabled when each layer only contains a single part."
msgstr "Met spiraliseren wordt de Z-beweging van de buitenrand vloeiender. Hierdoor ontstaat een geleidelijke Z-verhoging over de hele print. Met deze functie maakt u van een massief model een enkelwandige print met een solide bodem. Deze functie dient alleen te worden ingeschakeld wanneer elke laag uit een enkel deel bestaat."

msgctxt "material_standby_temperature label"
msgid "Standby Temperature"
msgstr "Stand-bytemperatuur"

msgctxt "machine_start_gcode label"
msgid "Start G-code"
msgstr "Start G-code"

msgctxt "z_seam_type description"
msgid "Starting point of each path in a layer. When paths in consecutive layers start at the same point a vertical seam may show on the print. When aligning these near a user specified location, the seam is easiest to remove. When placed randomly the inaccuracies at the paths' start will be less noticeable. When taking the shortest path the print will be quicker."
msgstr "Het startpunt voor elk pad in een laag. Wanneer paden in opeenvolgende lagen op hetzelfde punt beginnen, kan in de print een verticale naad zichtbaar zijn. De naad is het eenvoudigst te verwijderen wanneer deze zich nabij een door de gebruiker opgegeven locatie van de print bevindt. De onnauwkeurigheden vallen minder op wanneer het pad steeds op een willekeurige plek begint. De print is sneller af wanneer het kortste pad wordt gekozen."

msgctxt "machine_steps_per_mm_e label"
msgid "Steps per Millimeter (E)"
msgstr "Stappen per millimeter (E)"

msgctxt "machine_steps_per_mm_x label"
msgid "Steps per Millimeter (X)"
msgstr "Stappen per millimeter (X)"

msgctxt "machine_steps_per_mm_y label"
msgid "Steps per Millimeter (Y)"
msgstr "Stappen per millimeter (Y)"

msgctxt "machine_steps_per_mm_z label"
msgid "Steps per Millimeter (Z)"
msgstr "Stappen per millimeter (Z)"

msgctxt "support description"
msgid "Support"
msgstr "Supportstructuur"

msgctxt "support label"
msgid "Support"
msgstr "Supportstructuur"

msgctxt "acceleration_support label"
msgid "Support Acceleration"
msgstr "Acceleratie Supportstructuur"

msgctxt "support_bottom_distance label"
msgid "Support Bottom Distance"
msgstr "Afstand van Onderkant Supportstructuur"

msgctxt "support_bottom_wall_count label"
msgid "Support Bottom Wall Line Count"
msgstr "Aantal wandlijnen van de ondersteuningsbodem"

msgctxt "support_brim_line_count label"
msgid "Support Brim Line Count"
msgstr "Aantal supportbrimlijnen"

msgctxt "support_brim_width label"
msgid "Support Brim Width"
msgstr "Breedte supportbrim"

msgctxt "support_zag_skip_count label"
msgid "Support Chunk Line Count"
msgstr "Aantal Lijnen Supportstuk"

msgctxt "support_skip_zag_per_mm label"
msgid "Support Chunk Size"
msgstr "Grootte Supportstuk"

msgctxt "support_infill_rate label"
msgid "Support Density"
msgstr "Dichtheid Supportstructuur"

msgctxt "support_xy_overrides_z label"
msgid "Support Distance Priority"
msgstr "Prioriteit Afstand Supportstructuur"

msgctxt "support_extruder_nr label"
msgid "Support Extruder"
msgstr "Extruder Supportstructuur"

msgctxt "acceleration_support_bottom label"
msgid "Support Floor Acceleration"
msgstr "Acceleratie supportvloer"

msgctxt "support_bottom_density label"
msgid "Support Floor Density"
msgstr "Dichtheid supportvloer"

msgctxt "support_bottom_extruder_nr label"
msgid "Support Floor Extruder"
msgstr "Extruder supportvloer"

msgctxt "support_bottom_material_flow label"
msgid "Support Floor Flow"
msgstr "Doorvoer supportvloer"

msgctxt "support_bottom_offset label"
msgid "Support Floor Horizontal Expansion"
msgstr "Supportvloer horizontale uitbreiding"

msgctxt "jerk_support_bottom label"
msgid "Support Floor Jerk"
msgstr "Schok supportvloer"

msgctxt "support_bottom_angles label"
msgid "Support Floor Line Directions"
msgstr "Lijnrichting supportvloer"

msgctxt "support_bottom_line_distance label"
msgid "Support Floor Line Distance"
msgstr "Lijnafstand supportvloer"

msgctxt "support_bottom_line_width label"
msgid "Support Floor Line Width"
msgstr "Lijnbreedte supportvloer"

msgctxt "support_bottom_pattern label"
msgid "Support Floor Pattern"
msgstr "Patroon supportvloer"

msgctxt "speed_support_bottom label"
msgid "Support Floor Speed"
msgstr "Snelheid supportvloer"

msgctxt "support_bottom_height label"
msgid "Support Floor Thickness"
msgstr "Dikte supportvloer"

msgctxt "support_material_flow label"
msgid "Support Flow"
msgstr "Doorvoer support"

msgctxt "support_offset label"
msgid "Support Horizontal Expansion"
msgstr "Horizontale Uitzetting Supportstructuur"

msgctxt "acceleration_support_infill label"
msgid "Support Infill Acceleration"
msgstr "Acceleratie Supportvulling"

msgctxt "support_infill_extruder_nr label"
msgid "Support Infill Extruder"
msgstr "Extruder Supportvulling"

msgctxt "jerk_support_infill label"
msgid "Support Infill Jerk"
msgstr "Schok Supportvulling"

msgctxt "support_infill_sparse_thickness label"
msgid "Support Infill Layer Thickness"
msgstr "Dikte vullaag supportvulling"

msgctxt "support_infill_angles label"
msgid "Support Infill Line Directions"
msgstr "Lijnrichting Vulling Supportstructuur"

msgctxt "speed_support_infill label"
msgid "Support Infill Speed"
msgstr "Vulsnelheid Supportstructuur"

msgctxt "acceleration_support_interface label"
msgid "Support Interface Acceleration"
msgstr "Acceleratie Verbindingsstructuur"

msgctxt "support_interface_density label"
msgid "Support Interface Density"
msgstr "Dichtheid Verbindingsstructuur"

msgctxt "support_interface_extruder_nr label"
msgid "Support Interface Extruder"
msgstr "Extruder Verbindingsstructuur"

msgctxt "support_interface_material_flow label"
msgid "Support Interface Flow"
msgstr "Doorvoer supportinterface"

msgctxt "support_interface_offset label"
msgid "Support Interface Horizontal Expansion"
msgstr "Supportstructuur horizontale uitbreiding"

msgctxt "jerk_support_interface label"
msgid "Support Interface Jerk"
msgstr "Schok Verbindingsstructuur"

msgctxt "support_interface_angles label"
msgid "Support Interface Line Directions"
msgstr "Lijnrichting interface supportstructuur"

msgctxt "support_interface_line_width label"
msgid "Support Interface Line Width"
msgstr "Lijnbreedte Verbindingsstructuur"

msgctxt "support_interface_pattern label"
msgid "Support Interface Pattern"
msgstr "Patroon Verbindingsstructuur"

msgctxt "support_interface_priority label"
msgid "Support Interface Priority"
msgstr "Ondersteuning Interface Prioriteit"

msgctxt "speed_support_interface label"
msgid "Support Interface Speed"
msgstr "Vulsnelheid Verbindingsstructuur"

msgctxt "support_interface_height label"
msgid "Support Interface Thickness"
msgstr "Dikte Verbindingsstructuur"

msgctxt "support_interface_wall_count label"
msgid "Support Interface Wall Line Count"
msgstr "Ondersteuning Interface Wandlijn"

msgctxt "jerk_support label"
msgid "Support Jerk"
msgstr "Schok Supportstructuur"

msgctxt "support_join_distance label"
msgid "Support Join Distance"
msgstr "Samenvoegafstand Supportstructuur"

msgctxt "support_line_distance label"
msgid "Support Line Distance"
msgstr "Lijnafstand Supportstructuur"

msgctxt "support_line_width label"
msgid "Support Line Width"
msgstr "Lijnbreedte Supportstructuur"

msgctxt "support_mesh label"
msgid "Support Mesh"
msgstr "Supportstructuur raster"

msgctxt "support_angle label"
msgid "Support Overhang Angle"
msgstr "Overhanghoek Supportstructuur"

msgctxt "support_pattern label"
msgid "Support Pattern"
msgstr "Patroon Supportstructuur"

msgctxt "support_type label"
msgid "Support Placement"
msgstr "Plaatsing Supportstructuur"

msgctxt "acceleration_support_roof label"
msgid "Support Roof Acceleration"
msgstr "Acceleratie supportdak"

msgctxt "support_roof_density label"
msgid "Support Roof Density"
msgstr "Dichtheid supportdak"

msgctxt "support_roof_extruder_nr label"
msgid "Support Roof Extruder"
msgstr "Extruder supportdak"

msgctxt "support_roof_material_flow label"
msgid "Support Roof Flow"
msgstr "Doorvoer supportdak"

msgctxt "support_roof_offset label"
msgid "Support Roof Horizontal Expansion"
msgstr "Supportdak horizontale uitbreiding"

msgctxt "jerk_support_roof label"
msgid "Support Roof Jerk"
msgstr "Schok supportdak"

msgctxt "support_roof_angles label"
msgid "Support Roof Line Directions"
msgstr "Lijnrichting supportdak"

msgctxt "support_roof_line_distance label"
msgid "Support Roof Line Distance"
msgstr "Lijnafstand supportdak"

msgctxt "support_roof_line_width label"
msgid "Support Roof Line Width"
msgstr "Lijnbreedte supportdak"

msgctxt "support_roof_pattern label"
msgid "Support Roof Pattern"
msgstr "Patroon supportdak"

msgctxt "speed_support_roof label"
msgid "Support Roof Speed"
msgstr "Snelheid supportdak"

msgctxt "support_roof_height label"
msgid "Support Roof Thickness"
msgstr "Dikte Supportdak"

msgctxt "support_roof_wall_count label"
msgid "Support Roof Wall Line Count"
msgstr "Aantal wandlijnen ondersteuningsdak"

msgctxt "speed_support label"
msgid "Support Speed"
msgstr "Snelheid Supportstructuur"

msgctxt "support_bottom_stair_step_height label"
msgid "Support Stair Step Height"
msgstr "Hoogte Traptreden Supportstructuur"

msgctxt "support_bottom_stair_step_width label"
msgid "Support Stair Step Maximum Width"
msgstr "Maximale breedte traptreden supportstructuur"

msgctxt "support_bottom_stair_step_min_slope label"
msgid "Support Stair Step Minimum Slope Angle"
msgstr "Minimale hellingshoek traptreden supportstructuur"

msgctxt "support_structure label"
msgid "Support Structure"
msgstr "Supportstructuur"

msgctxt "support_top_distance label"
msgid "Support Top Distance"
msgstr "Afstand van Bovenkant Supportstructuur"

msgctxt "support_wall_count label"
msgid "Support Wall Line Count"
msgstr "Aantal wandlijnen supportstructuur"

msgctxt "support_xy_distance label"
msgid "Support X/Y Distance"
msgstr "X-/Y-afstand Supportstructuur"

msgctxt "support_z_distance label"
msgid "Support Z Distance"
msgstr "Z-afstand Supportstructuur"

msgctxt "support_interface_priority option support_lines_overwrite_interface_area"
msgid "Support lines preferred"
msgstr "Geprefereerde ondersteuningslijnen"

msgctxt "support_interface_priority option support_area_overwrite_interface_area"
msgid "Support preferred"
msgstr "Geprefereerde ondersteuning"

msgctxt "support_supported_skin_fan_speed label"
msgid "Supported Skin Fan Speed"
msgstr "Ondersteunde Ventilatorsnelheid Skin"

msgctxt "magic_mesh_surface_mode option surface"
msgid "Surface"
msgstr "Oppervlak"

msgctxt "material_surface_energy label"
msgid "Surface Energy"
msgstr "Oppervlakte-energie"

msgctxt "magic_mesh_surface_mode label"
msgid "Surface Mode"
msgstr "Oppervlaktemodus"

msgctxt "material_adhesion_tendency description"
msgid "Surface adhesion tendency."
msgstr "Hechtingsgevoeligheid van het oppervlak."

msgctxt "material_surface_energy description"
msgid "Surface energy."
msgstr "Oppervlakte-energie."

msgctxt "brim_smart_ordering description"
msgid "Swap print order of the innermost and second innermost brim lines. This improves brim removal."
msgstr "Verwissel de printvolgorde van de binnenste en de op een na binnenste randlijn. Dit verbetert het verwijderen van de rand."

msgctxt "alternate_carve_order description"
msgid "Switch to which mesh intersecting volumes will belong with every layer, so that the overlapping meshes become interwoven. Turning this setting off will cause one of the meshes to obtain all of the volume in the overlap, while it is removed from the other meshes."
msgstr "Schakel naar de rastersnijpuntvolumes die bij elke laag horen, zodat de overlappende rasters worden verweven. Als u deze instelling uitschakelt, krijgt een van de rasters al het volume in de overlap, terwijl dit uit de andere rasters wordt verwijderd."

msgctxt "adaptive_layer_height_threshold description"
msgid "Target horizontal distance between two adjacent layers. Reducing this setting causes thinner layers to be used to bring the edges of the layers closer together."
msgstr "Horizontale doelafstand tussen twee aangrenzende lagen. Als u deze instelling verkleint, worden dunnere lagen gebruikt om de randen van de lagen dichter bij elkaar te brengen."

msgctxt "layer_start_x description"
msgid "The X coordinate of the position near where to find the part to start printing each layer."
msgstr "De X-coördinaat van de positie nabij het deel waar met het printen van elke laag kan worden begonnen."

msgctxt "z_seam_x description"
msgid "The X coordinate of the position near where to start printing each part in a layer."
msgstr "De X-coördinaat van de positie nabij waar met het printen van elk deel van een laag moet worden begonnen."

msgctxt "extruder_prime_pos_x description"
msgid "The X coordinate of the position where the nozzle primes at the start of printing."
msgstr "De X-coördinaat van de positie waar filament in de nozzle wordt geprimed aan het begin van het printen."

msgctxt "layer_start_y description"
msgid "The Y coordinate of the position near where to find the part to start printing each layer."
msgstr "De Y-coördinaat van de positie nabij het deel waar met het printen van elke laag kan worden begonnen."

msgctxt "z_seam_y description"
msgid "The Y coordinate of the position near where to start printing each part in a layer."
msgstr "De Y-coördinaat van de positie nabij waar met het printen van elk deel van een laag moet worden begonnen."

msgctxt "extruder_prime_pos_y description"
msgid "The Y coordinate of the position where the nozzle primes at the start of printing."
msgstr "De Y-coördinaat van de positie waar filament in de nozzle wordt geprimed aan het begin van het printen."

msgctxt "extruder_prime_pos_z description"
msgid "The Z coordinate of the position where the nozzle primes at the start of printing."
msgstr "De Z-coördinaat van de positie waar filament in de nozzle wordt teruggeduwd aan het begin van het printen."

msgctxt "acceleration_print_layer_0 description"
msgid "The acceleration during the printing of the initial layer."
msgstr "De acceleratie tijdens het printen van de eerste laag."

msgctxt "acceleration_layer_0 description"
msgid "The acceleration for the initial layer."
msgstr "De acceleratie voor de eerste laag."

msgctxt "acceleration_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "De acceleratie tijdens het uitvoeren van bewegingen in de eerste laag."

msgctxt "jerk_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "De acceleratie tijdens het uitvoeren van bewegingen in de eerste laag."

msgctxt "acceleration_wall_x description"
msgid "The acceleration with which all inner walls are printed."
msgstr "De acceleratie tijdens het printen van alle binnenwanden."

msgctxt "acceleration_infill description"
msgid "The acceleration with which infill is printed."
msgstr "De acceleratie tijdens het printen van de vulling."

msgctxt "acceleration_ironing description"
msgid "The acceleration with which ironing is performed."
msgstr "De acceleratie tijdens het strijken."

msgctxt "acceleration_print description"
msgid "The acceleration with which printing happens."
msgstr "De acceleratie tijdens het printen."

msgctxt "raft_base_acceleration description"
msgid "The acceleration with which the base raft layer is printed."
msgstr "De acceleratie tijdens het printen van het grondvlak van de raft."

msgctxt "acceleration_support_bottom description"
msgid "The acceleration with which the floors of support are printed. Printing them at lower acceleration can improve adhesion of support on top of your model."
msgstr "De acceleratie tijdens het printen van de supportvloeren. Als u deze met een lagere acceleratie print, hecht het supportmateriaal beter aan de bovenzijde van het model."

msgctxt "acceleration_support_infill description"
msgid "The acceleration with which the infill of support is printed."
msgstr "De acceleratie tijdens het printen van de supportvulling."

msgctxt "raft_interface_acceleration description"
msgid "The acceleration with which the middle raft layer is printed."
msgstr "De acceleratie tijdens het printen van de middelste laag van de raft."

msgctxt "acceleration_wall_0 description"
msgid "The acceleration with which the outermost walls are printed."
msgstr "De acceleratie tijdens het printen van de buitenste wanden."

msgctxt "acceleration_prime_tower description"
msgid "The acceleration with which the prime tower is printed."
msgstr "De acceleratie tijdens het printen van de primepijler."

msgctxt "raft_acceleration description"
msgid "The acceleration with which the raft is printed."
msgstr "De acceleratie tijdens het printen van de raft."

msgctxt "acceleration_support_interface description"
msgid "The acceleration with which the roofs and floors of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "De acceleratie tijdens het printen van de supportdaken en -vloeren. Als u deze met een lagere acceleratie print, wordt de kwaliteit van de overhang verbeterd."

msgctxt "acceleration_support_roof description"
msgid "The acceleration with which the roofs of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "De acceleratie tijdens het printen van de supportdaken. Als u deze met een lagere acceleratie print, wordt de kwaliteit van de overhang verbeterd."

msgctxt "acceleration_skirt_brim description"
msgid "The acceleration with which the skirt and brim are printed. Normally this is done with the initial layer acceleration, but sometimes you might want to print the skirt or brim at a different acceleration."
msgstr "De acceleratie tijdens het printen van de skirt en de brim. Normaal gebeurt dit met dezelfde acceleratie als die van de eerste laag, maar in sommige situaties wilt u de skirt of de brim wellicht met een andere acceleratie printen."

msgctxt "acceleration_support description"
msgid "The acceleration with which the support structure is printed."
msgstr "De acceleratie tijdens het printen van de supportstructuur."

msgctxt "raft_surface_acceleration description"
msgid "The acceleration with which the top raft layers are printed."
msgstr "De acceleratie tijdens het printen van de toplagen van de raft."

msgctxt "acceleration_wall_x_roofing description"
msgid "The acceleration with which the top surface inner walls are printed."
msgstr "De versnelling waarmee de binnenwanden van het bovenoppervlak worden geprint."

msgctxt "acceleration_wall_0_roofing description"
msgid "The acceleration with which the top surface outermost walls are printed."
msgstr "De versnelling waarmee de buitenste muren van het bovenoppervlak worden geprint."

msgctxt "acceleration_wall description"
msgid "The acceleration with which the walls are printed."
msgstr "De acceleratie tijdens het printen van de wanden."

msgctxt "acceleration_roofing description"
msgid "The acceleration with which top surface skin layers are printed."
msgstr "De acceleratie tijdens het printen van de bovenste skinlagen."

msgctxt "acceleration_topbottom description"
msgid "The acceleration with which top/bottom layers are printed."
msgstr "De acceleratie tijdens het printen van de boven-/onderlagen."

msgctxt "acceleration_travel description"
msgid "The acceleration with which travel moves are made."
msgstr "De acceleratie tijdens het uitvoeren van bewegingen."

msgctxt "ironing_flow description"
msgid "The amount of material, relative to a normal skin line, to extrude during ironing. Keeping the nozzle filled helps filling some of the crevices of the top surface, but too much results in overextrusion and blips on the side of the surface."
msgstr "De hoeveelheid materiaal, in verhouding tot een normale skinlijn, die tijdens het strijken moet worden doorgevoerd. Als de nozzle gevuld blijft, kunnen scheuren in de bovenlaag worden gevuld. Te hoge doorvoer leidt echter tot uitstulpingen aan de zijkant van het oppervlak."

msgctxt "infill_overlap description"
msgid "The amount of overlap between the infill and the walls as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "De mate van overlap tussen de vulling en de wanden als percentage van de lijnbreedte van de vulling. Met een lichte overlap kunnen de wanden goed hechten aan de vulling."

msgctxt "infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls. A slight overlap allows the walls to connect firmly to the infill."
msgstr "De mate van overlap tussen de vulling en de wanden. Met een lichte overlap kunnen de wanden goed hechten aan de vulling."

msgctxt "switch_extruder_retraction_amount description"
msgid "The amount of retraction when switching extruders. Set to 0 for no retraction at all. This should generally be the same as the length of the heat zone."
msgstr "De intrekafstand wanneer de extruders worden gewisseld. Als u deze optie instelt op 0, wordt er niet ingetrokken. Deze waarde dient doorgaans gelijk te zijn aan de lengte van de verwarmingszone."

msgctxt "machine_nozzle_expansion_angle description"
msgid "The angle between the horizontal plane and the conical part right above the tip of the nozzle."
msgstr "De hoek tussen het horizontale vlak en het conische gedeelte boven de punt van de nozzle."

msgctxt "support_tower_roof_angle description"
msgid "The angle of a rooftop of a tower. A higher value results in pointed tower roofs, a lower value results in flattened tower roofs."
msgstr "De hoek van een dak van een pijler. Een hogere waarde zorgt voor een spits pijlerdak, een lagere waarde zorgt voor een plat pijlerdak."

msgctxt "mold_angle description"
msgid "The angle of overhang of the outer walls created for the mold. 0° will make the outer shell of the mold vertical, while 90° will make the outside of the model follow the contour of the model."
msgstr "De hoek van de overhang van de buitenwanden die voor de matrijs worden gemaakt. Met 0° is de buitenshell van de matrijs verticaal, terwijl met 90° de buitenzijde van de matrijs de contouren van het model volgt."

msgctxt "support_tree_branch_diameter_angle description"
msgid "The angle of the branches' diameter as they gradually become thicker towards the bottom. An angle of 0 will cause the branches to have uniform thickness over their length. A bit of an angle can increase stability of the tree support."
msgstr "De hoek van de diameter van de takken terwijl ze naar beneden toe geleidelijk dikker worden. Met de hoekinstelling 0 zijn de takken over de gehele lengte even dik. Een kleine hoek verbetert de stabiliteit van de boomsupportstructuur."

msgctxt "support_conical_angle description"
msgid "The angle of the tilt of conical support. With 0 degrees being vertical, and 90 degrees being horizontal. Smaller angles cause the support to be more sturdy, but consist of more material. Negative angles cause the base of the support to be wider than the top."
msgstr "De hoek van de schuine kant van de conische supportstructuur, waarbij 0 graden verticaal en 90 horizontaal is. Met een kleinere hoek is de supportstructuur steviger, maar bestaat deze uit meer materiaal. Met een negatieve hoek is het grondvlak van de supportstructuur breder dan de top."

msgctxt "magic_fuzzy_skin_point_density description"
msgid "The average density of points introduced on each polygon in a layer. Note that the original points of the polygon are discarded, so a low density results in a reduction of the resolution."
msgstr "De gemiddelde dichtheid van de punten die op elke polygoon in een laag worden geplaatst. Houd er rekening mee dat de originele punten van de polygoon worden verwijderd. Een lage dichtheid leidt dus tot een verlaging van de resolutie."

msgctxt "magic_fuzzy_skin_point_dist description"
msgid "The average distance between the random points introduced on each line segment. Note that the original points of the polygon are discarded, so a high smoothness results in a reduction of the resolution. This value must be higher than half the Fuzzy Skin Thickness."
msgstr "De gemiddelde afstand tussen de willekeurig geplaatste punten op elk lijnsegment. Houd er rekening mee dat de originele punten van de polygoon worden verwijderd. Een hoge effenheid leidt dus tot een verlaging van de resolutie. Deze waarde moet hoger zijn dan de helft van de Dikte rafelig oppervlak."

msgctxt "material_brand description"
msgid "The brand of material used."
msgstr "Het merk van het materiaal dat gebruikt wordt."

msgctxt "machine_acceleration description"
msgid "The default acceleration of print head movement."
msgstr "De standaardacceleratie van de printkopbeweging."

msgctxt "default_material_print_temperature description"
msgid "The default temperature used for printing. This should be the \"base\" temperature of a material. All other print temperatures should use offsets based on this value"
msgstr "De standaardtemperatuur waarmee wordt geprint. Dit moet overeenkomen met de basistemperatuur van een materiaal. Voor alle andere printtemperaturen moet een offset worden gebruikt die gebaseerd is op deze waarde"

msgctxt "default_material_bed_temperature description"
msgid "The default temperature used for the heated build plate. This should be the \"base\" temperature of a build plate. All other print temperatures should use offsets based on this value"
msgstr "De standaardtemperatuur die wordt gebruikt voor het verwarmde platform. Dit moet overeenkomen met de basistemperatuur van een platform. Voor alle andere printtemperaturen moet een offset worden gebruikt die is gebaseerd op deze waarde"

msgctxt "bridge_skin_density description"
msgid "The density of the bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "De dichtheid van de brugskinlaag. Met een waarde lager dan 100 worden de ruimten tussen de skinlijnen groter."

msgctxt "support_bottom_density description"
msgid "The density of the floors of the support structure. A higher value results in better adhesion of the support on top of the model."
msgstr "De dichtheid van de vloeren van de supportstructuur. Met een hogere waarde hecht het supportmateriaal beter aan de bovenzijde van het model."

msgctxt "support_roof_density description"
msgid "The density of the roofs of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "De dichtheid van de daken van de supportstructuur. Met een hogere waarde krijgt u een betere overhang, maar is de supportstructuur moeilijker te verwijderen."

msgctxt "bridge_skin_density_2 description"
msgid "The density of the second bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "De dichtheid van de tweede brugskinlaag. Met een waarde lager dan 100 worden de ruimten tussen de skinlijnen groter."

msgctxt "bridge_skin_density_3 description"
msgid "The density of the third bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "De dichtheid van de derde brugskinlaag. Met een waarde lager dan 100 worden de ruimten tussen de skinlijnen groter."

msgctxt "machine_depth description"
msgid "The depth (Y-direction) of the printable area."
msgstr "De diepte (Y-richting) van het printbare gebied."

msgctxt "support_tower_diameter description"
msgid "The diameter of a special tower."
msgstr "De diameter van een speciale pijler."

msgctxt "support_tree_branch_diameter description"
msgid "The diameter of the thinnest branches of tree support. Thicker branches are more sturdy. Branches towards the base will be thicker than this."
msgstr "Hiermee stelt u de diameter in van de dunste takken van de boomsupportstructuur. Dikkere takken zijn steviger. Takken die dichter bij de stam liggen, zijn dikker dan dit."

msgctxt "support_tree_tip_diameter description"
msgid "The diameter of the top of the tip of the branches of tree support."
msgstr "De diameter van de bovenkant van de punt van de takken van de boomsteun."

msgctxt "machine_feeder_wheel_diameter description"
msgid "The diameter of the wheel that drives the material in the feeder."
msgstr "De diameter van het tandwiel waarmee het materiaal in de feeder wordt gevoerd."

msgctxt "support_tree_max_diameter description"
msgid "The diameter of the widest branches of tree support. A thicker trunk is more sturdy; a thinner trunk takes up less space on the build plate."
msgstr "De diameter van de breedste takken van de boomondersteuning. Een dikkere tak is steviger; een dunnere tak neemt minder ruimte in beslag op het platform."

msgctxt "adaptive_layer_height_variation_step description"
msgid "The difference in height of the next layer height compared to the previous one."
msgstr "Het hoogteverschil tussen de hoogte van de volgende laag ten opzichte van de vorige laag."

msgctxt "ironing_line_spacing description"
msgid "The distance between the lines of ironing."
msgstr "De afstand tussen de strijklijnen."

msgctxt "travel_avoid_distance description"
msgid "The distance between the nozzle and already printed parts when avoiding during travel moves."
msgstr "De afstand tussen de nozzle en geprinte delen wanneer deze tijdens bewegingen worden gemeden."

msgctxt "raft_base_line_spacing description"
msgid "The distance between the raft lines for the base raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "De afstand tussen de lijnen in de onderste laag van de raft. Als u hier een brede tussenruimte instelt, kan de raft eenvoudiger van het platform worden verwijderd."

msgctxt "raft_interface_line_spacing description"
msgid "The distance between the raft lines for the middle raft layer. The spacing of the middle should be quite wide, while being dense enough to support the top raft layers."
msgstr "De afstand tussen de raftlijnen voor de middelste laag van de raft. De ruimte in het midden moet vrij breed zijn, maar toch smal genoeg om ondersteuning te bieden voor de bovenste lagen van de raft."

msgctxt "raft_surface_line_spacing description"
msgid "The distance between the raft lines for the top raft layers. The spacing should be equal to the line width, so that the surface is solid."
msgstr "De afstand tussen de raftlijnen voor de bovenste lagen van de raft. Als u een solide oppervlak wilt maken, moet de ruimte gelijk zijn aan de lijnbreedte."

msgctxt "prime_tower_raft_base_line_spacing description"
msgid "The distance between the raft lines for the unique prime tower raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "De afstand tussen de vlotlijnen voor de unieke vlotlaag van de Prime Tower. Een brede afstand maakt het eenvoudig om het vlot van de bouwplaat te verwijderen."

msgctxt "interlocking_depth description"
msgid "The distance from the boundary between models to generate interlocking structure, measured in cells. Too few cells will result in poor adhesion."
msgstr "De afstand vanaf de grens tussen modellen om een in elkaar grijpende structuur te genereren, gemeten in cellen. Te weinig cellen leiden tot slechte hechting."

msgctxt "brim_width description"
msgid "The distance from the model to the outermost brim line. A larger brim enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "De afstand vanaf de rand van het model tot de buitenrand van de brim. Een bredere brim hecht beter aan het platform, maar verkleint uw effectieve printgebied."

msgctxt "interlocking_boundary_avoidance description"
msgid "The distance from the outside of a model where interlocking structures will not be generated, measured in cells."
msgstr "De afstand van de buitenkant van een model waarbij in elkaar grijpende structuren niet worden gegenereerd, gemeten in cellen."

msgctxt "machine_heat_zone_length description"
msgid "The distance from the tip of the nozzle in which heat from the nozzle is transferred to the filament."
msgstr "De afstand tussen de punt van de nozzle waarin de warmte uit de nozzle wordt overgedragen aan het filament."

msgctxt "bottom_skin_expand_distance description"
msgid "The distance the bottom skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the skin adhere better to the walls on the layer below. Lower values save amount of material used."
msgstr "De afstand waarmee de onderste skinlagen worden uitgebreid in de vulling. Bij hogere waarden hecht de skin beter aan het vulpatroon en de wanden van de onderliggende laag. Bij lagere waarden wordt er minder materiaal gebruikt."

msgctxt "expand_skins_expand_distance description"
msgid "The distance the skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on neighboring layers adhere better to the skin. Lower values save amount of material used."
msgstr "De afstand waarmee de skin wordt uitgebreid in de vulling. Bij hogere waarden hecht de skin beter aan het vulpatroon en hechten de wanden van aangrenzende lagen beter aan de skin. Bij lagere waarden wordt er minder materiaal gebruikt."

msgctxt "top_skin_expand_distance description"
msgid "The distance the top skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on the layer above adhere better to the skin. Lower values save amount of material used."
msgstr "De afstand waarmee de bovenste skinlagen worden uitgebreid in de vulling. Bij hogere waarden hecht de skin beter aan het vulpatroon en hechten de wanden op de bovenliggende laag beter aan de skin. Bij lagere waarden wordt er minder materiaal gebruikt."

msgctxt "wipe_move_distance description"
msgid "The distance to move the head back and forth across the brush."
msgstr "De afstand die de kop heen en weer wordt bewogen over de borstel."

msgctxt "lightning_infill_prune_angle description"
msgid "The endpoints of infill lines are shortened to save on material. This setting is the angle of overhang of the endpoints of these lines."
msgstr "De eindpunten van de vullijnen worden verkort om materiaal te besparen. Deze instelling is de overhanghoek van de eindpunten van deze lijnen."

msgctxt "material_extrusion_cool_down_speed description"
msgid "The extra speed by which the nozzle cools while extruding. The same value is used to signify the heat up speed lost when heating up while extruding."
msgstr "De extra snelheid waarmee de nozzle tijdens het doorvoeren afkoelt. Met dezelfde waarde wordt ook de verloren verwarmingssnelheid aangeduid wanneer tijdens het doorvoeren wordt verwarmd."

msgctxt "support_extruder_nr_layer_0 description"
msgid "The extruder train to use for printing the first layer of support infill. This is used in multi-extrusion."
msgstr "De extruder train die wordt gebruikt voor het printen van de eerste laag van de supportvulling. Deze optie wordt gebruikt in meervoudige doorvoer."

msgctxt "raft_base_extruder_nr description"
msgid "The extruder train to use for printing the first layer of the raft. This is used in multi-extrusion."
msgstr "De extruder train die wordt gebruikt voor het printen van de eerste laag van de raft. Deze optie wordt gebruikt bij meervoudige doorvoer."

msgctxt "support_bottom_extruder_nr description"
msgid "The extruder train to use for printing the floors of the support. This is used in multi-extrusion."
msgstr "De extruder train die wordt gebruikt voor het printen van de supportvloeren. Deze optie wordt gebruikt in meervoudige doorvoer."

msgctxt "support_infill_extruder_nr description"
msgid "The extruder train to use for printing the infill of the support. This is used in multi-extrusion."
msgstr "De extruder train die wordt gebruikt voor het printen van de supportvulling. Deze optie wordt gebruikt in meervoudige doorvoer."

msgctxt "raft_interface_extruder_nr description"
msgid "The extruder train to use for printing the middle layer of the raft. This is used in multi-extrusion."
msgstr "De extruder train die wordt gebruikt voor het printen van de middelste laag van de raft. Deze optie wordt gebruikt bij meervoudige doorvoer."

msgctxt "support_interface_extruder_nr description"
msgid "The extruder train to use for printing the roofs and floors of the support. This is used in multi-extrusion."
msgstr "De extruder train die wordt gebruikt voor het printen van de daken en vloeren van de supportstructuur. Deze optie wordt gebruikt in meervoudige doorvoer."

msgctxt "support_roof_extruder_nr description"
msgid "The extruder train to use for printing the roofs of the support. This is used in multi-extrusion."
msgstr "De extruder train die wordt gebruikt voor het printen van de supportdaken. Deze optie wordt gebruikt in meervoudige doorvoer."

msgctxt "skirt_brim_extruder_nr description"
msgid "The extruder train to use for printing the skirt or brim. This is used in multi-extrusion."
msgstr "De extruder train die wordt gebruikt voor het printen van de skirt/brim. Deze optie wordt gebruikt bij meervoudige doorvoer."

msgctxt "adhesion_extruder_nr description"
msgid "The extruder train to use for printing the skirt/brim/raft. This is used in multi-extrusion."
msgstr "De extruder train die wordt gebruikt voor het printen van de skirt/brim/raft. Deze optie wordt gebruikt in meervoudige doorvoer."

msgctxt "support_extruder_nr description"
msgid "The extruder train to use for printing the support. This is used in multi-extrusion."
msgstr "De extruder train die wordt gebruikt voor het printen van de supportstructuur. Deze optie wordt gebruikt in meervoudige doorvoer."

msgctxt "raft_surface_extruder_nr description"
msgid "The extruder train to use for printing the top layer(s) of the raft. This is used in multi-extrusion."
msgstr "De extruder train die wordt gebruikt voor het printen van de bovenste laag/lagen van de raft. Deze optie wordt gebruikt bij meervoudige doorvoer."

msgctxt "infill_extruder_nr description"
msgid "The extruder train used for printing infill. This is used in multi-extrusion."
msgstr "De extruder train die voor het printen van de vulling wordt gebruikt. Deze wordt gebruikt in meervoudige doorvoer."

msgctxt "wall_x_extruder_nr description"
msgid "The extruder train used for printing the inner walls. This is used in multi-extrusion."
msgstr "De extruder train die voor het printen van de binnenwanden wordt gebruikt. Deze wordt gebruikt in meervoudige doorvoer."

msgctxt "wall_0_extruder_nr description"
msgid "The extruder train used for printing the outer wall. This is used in multi-extrusion."
msgstr "De extruder train die voor het printen van de buitenwand wordt gebruikt. Deze wordt gebruikt in meervoudige doorvoer."

msgctxt "top_bottom_extruder_nr description"
msgid "The extruder train used for printing the top and bottom skin. This is used in multi-extrusion."
msgstr "De extruder train die voor het printen van de boven- en onderskin wordt gebruikt. Deze wordt gebruikt in meervoudige doorvoer."

msgctxt "roofing_extruder_nr description"
msgid "The extruder train used for printing the top most skin. This is used in multi-extrusion."
msgstr "De extruder train die voor het printen van de bovenste skinlaag wordt gebruikt. Deze wordt gebruikt in meervoudige doorvoer."

msgctxt "wall_extruder_nr description"
msgid "The extruder train used for printing the walls. This is used in multi-extrusion."
msgstr "De extruder train die voor het printen van de wanden wordt gebruikt. Deze wordt gebruikt in meervoudige doorvoer."

msgctxt "raft_base_fan_speed description"
msgid "The fan speed for the base raft layer."
msgstr "De ventilatorsnelheid tijdens het printen van de grondlaag van de raft."

msgctxt "raft_interface_fan_speed description"
msgid "The fan speed for the middle raft layer."
msgstr "De ventilatorsnelheid tijdens het printen van de middelste laag van de raft."

msgctxt "raft_fan_speed description"
msgid "The fan speed for the raft."
msgstr "De ventilatorsnelheid tijdens het printen van de raft."

msgctxt "raft_surface_fan_speed description"
msgid "The fan speed for the top raft layers."
msgstr "De ventilatorsnelheid tijdens het printen van de toplagen van de raft."

msgctxt "cross_infill_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the infill of the print."
msgstr "De bestandslocatie van een afbeelding waarvan de helderheidswaarden de minimale dichtheid op de bijbehorende locatie in de vulling van de print bepalen."

msgctxt "cross_support_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the support."
msgstr "De bestandslocatie van een afbeelding waarvan de helderheidswaarden de minimale dichtheid op de bijbehorende locatie in de supportstructuur bepalen."

msgctxt "speed_slowdown_layers description"
msgid "The first few layers are printed slower than the rest of the model, to get better adhesion to the build plate and improve the overall success rate of prints. The speed is gradually increased over these layers."
msgstr "De eerste lagen worden minder snel geprint dan de rest van het model, om ervoor te zorgen dat dit zich beter hecht aan het platform en om de kans dat de print slaagt te vergroten. Tijdens het printen van deze lagen wordt de snelheid geleidelijk opgevoerd."

msgctxt "raft_airgap description"
msgid "The gap between the final raft layer and the first layer of the model. Only the first layer is raised by this amount to lower the bonding between the raft layer and the model. Makes it easier to peel off the raft."
msgstr "De ruimte tussen de laatste laag van de raft en de eerste laag van het model. Alleen de eerste laag wordt met deze waarde verhoogd om de binding tussen de raftlaag en het model te verminderen. Hierdoor is het eenvoudiger om de raft te verwijderen."

msgctxt "machine_height description"
msgid "The height (Z-direction) of the printable area."
msgstr "De hoogte (Z-richting) van het printbare gebied."

msgctxt "mold_roof_height description"
msgid "The height above horizontal parts in your model which to print mold."
msgstr "De hoogte die in de matrijs moet worden geprint boven de horizontale delen in het model."

msgctxt "cool_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr "De hoogte waarop de ventilatoren op normale snelheid draaien. Tijdens het printen van de onderliggende lagen wordt de ventilatorsnelheid geleidelijk verhoogd van de startsnelheid ventilator naar de normale ventilatorsnelheid."

msgctxt "gantry_height description"
msgid "The height difference between the tip of the nozzle and the gantry system (X and Y axes)."
msgstr "Het hoogteverschil tussen de punt van de nozzle en het rijbrugsysteem (X- en Y-as)."

msgctxt "machine_nozzle_head_distance description"
msgid "The height difference between the tip of the nozzle and the lowest part of the print head."
msgstr "Het hoogteverschil tussen de punt van de nozzle en het laagste deel van de printkop."

msgctxt "retraction_hop_after_extruder_switch_height description"
msgid "The height difference when performing a Z Hop after extruder switch."
msgstr "Het hoogteverschil dat wordt aangehouden tijdens een Z-sprong na wisselen extruder."

msgctxt "retraction_hop description"
msgid "The height difference when performing a Z Hop."
msgstr "Het hoogteverschil dat wordt aangehouden tijdens een Z-sprong."

msgctxt "wipe_hop_amount description"
msgid "The height difference when performing a Z Hop."
msgstr "Het hoogteverschil dat wordt aangehouden tijdens een Z-sprong."

msgctxt "layer_height description"
msgid "The height of each layer in mm. Higher values produce faster prints in lower resolution, lower values produce slower prints in higher resolution."
msgstr "De hoogte van elke laag in mm. Met hogere waarden print u sneller met een lagere resolutie, met lagere waarden print u langzamer met een hogere resolutie."

msgctxt "gradual_infill_step_height description"
msgid "The height of infill of a given density before switching to half the density."
msgstr "De hoogte van de vulling van een opgegeven dichtheid voordat wordt overgeschakeld naar de helft van deze dichtheid."

msgctxt "gradual_support_infill_step_height description"
msgid "The height of support infill of a given density before switching to half the density."
msgstr "De hoogte van de supportvulling van een bepaalde dichtheid voordat de dichtheid wordt gehalveerd."

msgctxt "interlocking_beam_layer_count description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr "De hoogte van de balken van de in elkaar grijpende structuur, gemeten in aantal lagen. Minder lagen is sterker, maar vatbaarder voor defecten."

msgctxt "interlocking_orientation description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr "De hoogte van de balken van de in elkaar grijpende structuur, gemeten in aantal lagen. Minder lagen is sterker, maar vatbaarder voor defecten."

msgctxt "layer_height_0 description"
msgid "The height of the initial layer in mm. A thicker initial layer makes adhesion to the build plate easier."
msgstr "De hoogte van de eerste laag in mm. Met een dikkere eerste laag hecht het object beter aan het platform."

msgctxt "prime_tower_base_height description"
msgid "The height of the prime tower base. Increasing this value will result in a more sturdy prime tower because the base will be wider. If this setting is too low, the prime tower will not have a sturdy base."
msgstr "De hoogte van de basis van de prime toren. Het verhogen van deze waarde resulteert in een stevigere prime toren omdat de basis breder zal zijn. Als deze instelling te laag is, zal de prime toren geen stevige basis hebben."

msgctxt "support_bottom_stair_step_height description"
msgid "The height of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures. Set to zero to turn off the stair-like behaviour."
msgstr "De hoogte van de treden van het trapvormige grondvlak van de supportstructuur die op het model rust. Wanneer u een lage waarde invoert, kan de supportstructuur minder gemakkelijk worden verwijderd. Wanneer u echter een te hoge waarde invoert, kan de supportstructuur instabiel worden. Stel deze waarde in op nul om het trapvormige gedrag uit te schakelen."

msgctxt "brim_gap description"
msgid "The horizontal distance between the first brim line and the outline of the first layer of the print. A small gap can make the brim easier to remove while still providing the thermal benefits."
msgstr "De horizontale afstand tussen de eerste brimlijn en de contour van de eerste laag van de print. Door een kleine tussenruimte is de brim gemakkelijker te verwijderen terwijl de thermische voordelen behouden blijven."

msgctxt "skirt_gap description"
msgid "The horizontal distance between the skirt and the first layer of the print.\nThis is the minimum distance. Multiple skirt lines will extend outwards from this distance."
msgstr "De horizontale afstand tussen de skirt en de eerste laag van de print."
"Dit is de minimumafstand. Als u meerdere skirtlijnen print, worden deze vanaf deze afstand naar buiten geprint."

msgctxt "lightning_infill_straightening_angle description"
msgid "The infill lines are straightened out to save on printing time. This is the maximum angle of overhang allowed across the length of the infill line."
msgstr "De vullijnen zijn rechtgetrokken om printtijd te besparen. Dit is de grootste overhanghoek die over de lengte van de vullijn is toegestaan."

msgctxt "infill_offset_x description"
msgid "The infill pattern is moved this distance along the X axis."
msgstr "Het vulpatroon wordt over deze afstand verplaatst langs de X-as."

msgctxt "infill_offset_y description"
msgid "The infill pattern is moved this distance along the Y axis."
msgstr "Het vulpatroon wordt over deze afstand verplaatst langs de Y-as."

msgctxt "machine_nozzle_size description"
msgid "The inner diameter of the nozzle. Change this setting when using a non-standard nozzle size."
msgstr "De binnendiameter van de nozzle. Verander deze instelling wanneer u een nozzle gebruikt die geen standaard formaat heeft."

msgctxt "raft_base_jerk description"
msgid "The jerk with which the base raft layer is printed."
msgstr "De schok tijdens het printen van het grondvlak van de raft."

msgctxt "raft_interface_jerk description"
msgid "The jerk with which the middle raft layer is printed."
msgstr "De schok tijdens het printen van de middelste laag van de raft."

msgctxt "raft_jerk description"
msgid "The jerk with which the raft is printed."
msgstr "De schok tijdens het printen van de raft."

msgctxt "raft_surface_jerk description"
msgid "The jerk with which the top raft layers are printed."
msgstr "De schok tijdens het printen van de toplagen van de raft."

msgctxt "bottom_skin_preshrink description"
msgid "The largest width of bottom skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing bottom skin at slanted surfaces in the model."
msgstr "De grootste breedte van delen van de onderste skingebieden die verwijderd moeten worden. Elk skingebied dat smaller is dan deze waarde, zal verdwijnen. Hiermee kan op tijd en materiaal worden bespaard bij het printen van de onderste skinlaag op schuine vlakken in het model."

msgctxt "skin_preshrink description"
msgid "The largest width of skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top/bottom skin at slanted surfaces in the model."
msgstr "De grootste breedte van skingebieden die verwijderd moeten worden. Elk skingebied dat smaller is dan deze waarde zal verdwijnen. Hiermee kan op tijd en materiaal worden bespaard bij het printen van de bovenste/onderste skinlaag op schuine vlakken in het model."

msgctxt "top_skin_preshrink description"
msgid "The largest width of top skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top skin at slanted surfaces in the model."
msgstr "De grootste breedte van delen van bovenste skingebieden die verwijderd moeten worden. Elk skingebied dat smaller is dan deze waarde, zal verdwijnen. Hiermee kan op tijd en materiaal worden bespaard bij het printen van de bovenste/onderste skinlaag op schuine vlakken in het model."

msgctxt "cool_fan_full_layer description"
msgid "The layer at which the fans spin on regular fan speed. If regular fan speed at height is set, this value is calculated and rounded to a whole number."
msgstr "De laag waarop de ventilatoren op normale snelheid draaien. Als de normale ventilatorsnelheid op hoogte ingeschakeld is, wordt deze waarde berekend en op een geheel getal afgerond."

msgctxt "cool_min_layer_time_fan_speed_max description"
msgid "The layer time which sets the threshold between regular fan speed and maximum fan speed. Layers that print slower than this time use regular fan speed. For faster layers the fan speed gradually increases towards the maximum fan speed."
msgstr "De laagtijd waarmee de drempelwaarde tussen de normale ventilatorsnelheid en de maximale ventilatorsnelheid wordt ingesteld. Voor lagen die langzamer worden geprint, draaien de ventilatoren op normale snelheid. Bij lagen die sneller worden geprint, draaien de ventilatoren op maximale snelheid."

msgctxt "retraction_amount description"
msgid "The length of material retracted during a retraction move."
msgstr "De lengte waarover het materiaal wordt ingetrokken tijdens een intrekbeweging."

msgctxt "prime_tower_base_curve_magnitude description"
msgid "The magnitude factor used for the slope of the prime tower base. If you increase this value, the base will become slimmer. If you decrease it, the base will become thicker."
msgstr "De groottefactor die gebruikt wordt voor de helling van de basis van de prime toren. Als u deze waarde verhoogt, wordt de basis slanker. Als u het verlaagt, wordt de basis dikker."

msgctxt "machine_buildplate_type description"
msgid "The material of the build plate installed on the printer."
msgstr "Het materiaal van het platform dat in de printer geïnstalleerd is."

msgctxt "adaptive_layer_height_variation description"
msgid "The maximum allowed height different from the base layer height."
msgstr "De maximaal toegestane hoogte ten opzichte van de grondlaaghoogte."

msgctxt "ooze_shield_angle description"
msgid "The maximum angle a part in the ooze shield will have. With 0 degrees being vertical, and 90 degrees being horizontal. A smaller angle leads to less failed ooze shields, but more material."
msgstr "De maximale hoek voor een deel van het uitloopscherm. Hierbij is 0 graden verticaal en 90 graden horizontaal. Een kleinere hoek leidt tot minder mislukte uitloopschermen, maar zorgt ervoor dat er meer materiaal wordt gebruikt."

msgctxt "conical_overhang_angle description"
msgid "The maximum angle of overhangs after the they have been made printable. At a value of 0° all overhangs are replaced by a piece of model connected to the build plate, 90° will not change the model in any way."
msgstr "De maximale hoek van een overhang nadat deze printbaar is gemaakt. Bij een hoek van 0° worden alle overhangende gedeelten vervangen door een deel van het model dat is verbonden met het platform; bij een hoek van 90° wordt het model niet gewijzigd."

msgctxt "support_tree_angle description"
msgid "The maximum angle of the branches while they grow around the model. Use a lower angle to make them more vertical and more stable. Use a higher angle to be able to have more reach."
msgstr "De maximale hoek van de takken terwijl ze rond het model groeien. Gebruik een lagere hoek om ze verticaler en stabieler te maken. Gebruik een hogere hoek om meer bereik te hebben."

msgctxt "conical_overhang_hole_size description"
msgid "The maximum area of a hole in the base of the model before it's removed by Make Overhang Printable.  Holes smaller than this will be retained.  A value of 0 mm² will fill all holes in the models base."
msgstr "Het maximale oppervlak van een gat in de basis van het model voordat het wordt verwijderd om de overhang printbaar te maken.  Gaten die kleiner zijn dan dit oppervlak worden behouden.  Bij een waarde van 0 mm² worden alle gaten in de basis van het model gevuld."

msgctxt "meshfix_maximum_deviation description"
msgid "The maximum deviation allowed when reducing the resolution for the Maximum Resolution setting. If you increase this, the print will be less accurate, but the g-code will be smaller. Maximum Deviation is a limit for Maximum Resolution, so if the two conflict the Maximum Deviation will always be held true."
msgstr "De maximaal toegestane afwijking tijdens het verlagen van de resolutie voor de instelling Maximale resolutie. Als u deze waarde verhoogt, wordt de print minder nauwkeurig, maar wordt de G-code kleiner. Maximale afwijking is een limiet voor Maximale resolutie, dus als de twee tegenstrijdig zijn, wordt de Maximale afwijking altijd aangehouden."

msgctxt "support_join_distance description"
msgid "The maximum distance between support structures in the X/Y directions. When separate structures are closer together than this value, the structures merge into one."
msgstr "De maximale afstand tussen de supportstructuren in de X- en Y-richting. Wanneer afzonderlijke structuren dichter bij elkaar staan dan deze waarde, worden deze samengevoegd tot één structuur."

msgctxt "flow_rate_max_extrusion_offset description"
msgid "The maximum distance in mm to move the filament to compensate for changes in flow rate."
msgstr "De maximale afstand in mm om het filament te verplaatsen om veranderingen in de stroomsnelheid te compenseren."

msgctxt "meshfix_maximum_extrusion_area_deviation description"
msgid "The maximum extrusion area deviation allowed when removing intermediate points from a straight line. An intermediate point may serve as width-changing point in a long straight line. Therefore, if it is removed, it will cause the line to have a uniform width and, as a result, lose (or gain) a bit of extrusion area. If you increase this you may notice slight under- (or over-) extrusion in between straight parallel walls, as more intermediate width-changing points will be allowed to be removed. Your print will be less accurate, but the g-code will be smaller."
msgstr "De maximaal toegestane afwijking van het doorvoergebied bij het verwijderen van tussenliggende punten van een rechte lijn. Een tussenliggend punt kan dienen als breedte-veranderend punt in een lange rechte lijn. Verwijdering van het punt leidt er dus toe dat de lijn een uniforme breedte krijgt en als gevolg daarvan een stuk van het doorvoergebied verliest (of wint). Als u deze waarde verhoogt, merkt u mogelijk een lichte onder- (of over-)doorvoer op tussen rechte parallele wanden, omdat er meer tussenliggende punten kunnen worden verwijderd die de breedte wijzigen. Uw print zal minder accuraat zijn, maar de g-code is kleiner."

msgctxt "jerk_print_layer_0 description"
msgid "The maximum instantaneous velocity change during the printing of the initial layer."
msgstr "De maximale onmiddellijke snelheidsverandering tijdens het printen van de eerste laag."

msgctxt "jerk_print description"
msgid "The maximum instantaneous velocity change of the print head."
msgstr "De maximale onmiddellijke snelheidsverandering van de printkop."

msgctxt "jerk_ironing description"
msgid "The maximum instantaneous velocity change while performing ironing."
msgstr "De maximale onmiddellijke snelheidsverandering tijdens het strijken."

msgctxt "jerk_wall_x description"
msgid "The maximum instantaneous velocity change with which all inner walls are printed."
msgstr "De maximale onmiddellijke snelheidsverandering tijdens het printen van alle binnenwanden."

msgctxt "jerk_infill description"
msgid "The maximum instantaneous velocity change with which infill is printed."
msgstr "De maximale onmiddellijke snelheidsverandering tijdens het printen van de vulling."

msgctxt "jerk_support_bottom description"
msgid "The maximum instantaneous velocity change with which the floors of support are printed."
msgstr "De maximale onmiddellijke snelheidsverandering tijdens het printen van de supportvloeren."

msgctxt "jerk_support_infill description"
msgid "The maximum instantaneous velocity change with which the infill of support is printed."
msgstr "De maximale onmiddellijke snelheidsverandering tijdens het printen van de supportvulling."

msgctxt "jerk_wall_0 description"
msgid "The maximum instantaneous velocity change with which the outermost walls are printed."
msgstr "De maximale onmiddellijke snelheidsverandering tijdens het printen van de buitenwanden."

msgctxt "jerk_prime_tower description"
msgid "The maximum instantaneous velocity change with which the prime tower is printed."
msgstr "De maximale onmiddellijke snelheidsverandering tijdens het printen van de primepijler."

msgctxt "jerk_support_interface description"
msgid "The maximum instantaneous velocity change with which the roofs and floors of support are printed."
msgstr "De maximale onmiddellijke snelheidsverandering tijdens het printen van de supportdaken en -vloeren."

msgctxt "jerk_support_roof description"
msgid "The maximum instantaneous velocity change with which the roofs of support are printed."
msgstr "De maximale onmiddellijke snelheidsverandering tijdens het printen van de supportdaken."

msgctxt "jerk_skirt_brim description"
msgid "The maximum instantaneous velocity change with which the skirt and brim are printed."
msgstr "De maximale onmiddellijke snelheidsverandering tijdens het printen van de skirt en de brim."

msgctxt "jerk_support description"
msgid "The maximum instantaneous velocity change with which the support structure is printed."
msgstr "De maximale onmiddellijke snelheidsverandering tijdens het printen van de supportstructuur."

msgctxt "jerk_wall_x_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface inner walls are printed."
msgstr "De maximale plotselinge snelheidsverandering waarmee de buitenste muren van het bovenoppervlak worden geprint."

msgctxt "jerk_wall_0_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface outermost walls are printed."
msgstr "De maximale plotselinge snelheidsverandering waarmee de binnenste muren van het bovenoppervlak worden geprint."

msgctxt "jerk_wall description"
msgid "The maximum instantaneous velocity change with which the walls are printed."
msgstr "De maximale onmiddellijke snelheidsverandering tijdens het printen van de wanden."

msgctxt "jerk_roofing description"
msgid "The maximum instantaneous velocity change with which top surface skin layers are printed."
msgstr "De maximale onmiddellijke snelheidsverandering tijdens het printen van de bovenste skinlagen."

msgctxt "jerk_topbottom description"
msgid "The maximum instantaneous velocity change with which top/bottom layers are printed."
msgstr "De maximale onmiddellijke snelheidsverandering tijdens het printen van de boven-/onderlagen."

msgctxt "jerk_travel description"
msgid "The maximum instantaneous velocity change with which travel moves are made."
msgstr "De maximale onmiddellijke snelheidsverandering tijdens het uitvoeren van bewegingen."

msgctxt "prime_tower_max_bridging_distance description"
msgid "The maximum length of the branches which may be printed over the air."
msgstr "De maximale lengte van de takken die via de lucht mogen worden geprint."

msgctxt "machine_max_feedrate_x description"
msgid "The maximum speed for the motor of the X-direction."
msgstr "De maximale snelheid van de motor in de X-richting."

msgctxt "machine_max_feedrate_y description"
msgid "The maximum speed for the motor of the Y-direction."
msgstr "De maximale snelheid van de motor in de Y-richting."

msgctxt "machine_max_feedrate_z description"
msgid "The maximum speed for the motor of the Z-direction."
msgstr "De maximale snelheid van de motor in de Z-richting."

msgctxt "machine_max_feedrate_e description"
msgid "The maximum speed of the filament."
msgstr "De maximale snelheid voor de doorvoer van het filament."

msgctxt "support_bottom_stair_step_width description"
msgid "The maximum width of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures."
msgstr "De maximale breedte van de treden van het trapvormige grondvlak van de supportstructuur die op het model rust. Wanneer u een lage waarde invoert, kan de supportstructuur minder gemakkelijk worden verwijderd. Wanneer u echter een te hoge waarde invoert, kan de supportstructuur instabiel worden."

msgctxt "mold_width description"
msgid "The minimal distance between the outside of the mold and the outside of the model."
msgstr "De minimale afstand tussen de buitenzijde van de matrijs en de buitenzijde van het model."

msgctxt "machine_minimum_feedrate description"
msgid "The minimal movement speed of the print head."
msgstr "De minimale bewegingssnelheid van de printkop."

msgctxt "material_initial_print_temperature description"
msgid "The minimal temperature while heating up to the Printing Temperature at which printing can already start."
msgstr "De minimale temperatuur tijdens het opwarmen naar de printtemperatuur waarbij met printen kan worden begonnen."

msgctxt "machine_min_cool_heat_time_window description"
msgid "The minimal time an extruder has to be inactive before the nozzle is cooled. Only when an extruder is not used for longer than this time will it be allowed to cool down to the standby temperature."
msgstr "De minimale tijd die een extruder inactief moet zijn, voordat de nozzle wordt afgekoeld. Alleen als een extruder gedurende langer dan deze tijd niet wordt gebruikt, wordt deze afgekoeld naar de stand-bytemperatuur."

msgctxt "infill_support_angle description"
msgid "The minimum angle of internal overhangs for which infill is added. At a value of 0° objects are totally filled with infill, 90° will not provide any infill."
msgstr "De minimale interne overhanghoek waarbij vulling wordt toegevoegd. Bij een waarde van 0° worden objecten volledig gevuld. Bij 90° wordt er geen vulling geprint."

msgctxt "support_angle description"
msgid "The minimum angle of overhangs for which support is added. At a value of 0° all overhangs are supported, 90° will not provide any support."
msgstr "De minimale overhanghoek waarbij een supportstructuur wordt toegevoegd. Bij een waarde van 0° wordt elke overhang ondersteund. Bij 90° wordt er geen supportstructuur geprint."

msgctxt "retraction_min_travel description"
msgid "The minimum distance of travel needed for a retraction to happen at all. This helps to get fewer retractions in a small area."
msgstr "De minimale bewegingsafstand voordat het filament kan worden ingetrokken. Hiermee vermindert u het aantal intrekkingen in een klein gebied."

msgctxt "skirt_brim_minimal_length description"
msgid "The minimum length of the skirt or brim. If this length is not reached by all skirt or brim lines together, more skirt or brim lines will be added until the minimum length is reached. Note: If the line count is set to 0 this is ignored."
msgstr "De minimale lengte van de skirt of de brim. Als deze minimumlengte niet wordt bereikt met het totale aantal skirt- of brimlijnen, worden er meer skirt- of brimlijnen toegevoegd totdat de minimale lengte is bereikt. Opmerking: als het aantal lijnen is ingesteld op 0, wordt dit genegeerd."

msgctxt "min_odd_wall_line_width description"
msgid "The minimum line width for middle line gap filler polyline walls. This setting determines at which model thickness we switch from printing two wall lines, to printing two outer walls and a single central wall in the middle. A higher Minimum Odd Wall Line Width leads to a higher maximum even wall line width. The maximum odd wall line width is calculated as 2 * Minimum Even Wall Line Width."
msgstr "De minimum lijnbreedte voor opvuller voor ruimte middelste lijn bij muren met meerdere lijnen. Deze instelling bepaalt bij welke modeldikte we overschakelen van het printen van twee wandlijnen naar het printen van twee buitenwanden en één centrale wand in het midden. Een hogere Minimum breedte ongelijkmatige wandlijn leidt naar een hogere maximale lijnbreedte bij een gelijkmatige wand. De maximale breedte ongelijkmatige wandlijn wordt berekend als 2 * Minimum breedte gelijkmatige wandlijn."

msgctxt "min_even_wall_line_width description"
msgid "The minimum line width for normal polygonal walls. This setting determines at which model thickness we switch from printing a single thin wall line, to printing two wall lines. A higher Minimum Even Wall Line Width leads to a higher maximum odd wall line width. The maximum even wall line width is calculated as Outer Wall Line Width + 0.5 * Minimum Odd Wall Line Width."
msgstr "De minimum lijnbreedte voor normale polygonale wanden. Deze instelling bepaalt bij welke modeldikte we overschakelen van het printen van één dunne wandlijn op het printen van twee wandlijnen. Een hogere Minimum lijnbreedte gelijkmatige wand leidt tot een hogere maximum lijnbreedte voor een ongelijkmatige wand. De maximum breedte bij een gelijkmatige wandlijn wordt berekend als Lijnbreedte buitenste wand + 0,5 * Minimum lijnbreedte voor een ongelijkmatige wand."

msgctxt "cool_min_speed description"
msgid "The minimum print speed, despite slowing down due to the minimum layer time. When the printer would slow down too much, the pressure in the nozzle would be too low and result in bad print quality."
msgstr "De minimale printsnelheid die wordt aangehouden ondanks vertragen vanwege de minimale laagtijd. Als de printer te zeer vertraagt, wordt de druk in de nozzle te laag, wat leidt tot slechte printkwaliteit."

msgctxt "meshfix_maximum_resolution description"
msgid "The minimum size of a line segment after slicing. If you increase this, the mesh will have a lower resolution. This may allow the printer to keep up with the speed it has to process g-code and will increase slice speed by removing details of the mesh that it can't process anyway."
msgstr "Het minimale formaat van een lijnsegment na het slicen. Als u deze waarde verhoogt, wordt het model met een lagere resolutie geprint. Hiermee kan de printer de verwerkingssnelheid van de G-code bijhouden en wordt de slicesnelheid verhoogd doordat details van het raster worden verwijderd die niet kunnen worden verwerkt."

msgctxt "meshfix_maximum_travel_resolution description"
msgid "The minimum size of a travel line segment after slicing. If you increase this, the travel moves will have less smooth corners. This may allow the printer to keep up with the speed it has to process g-code, but it may cause model avoidance to become less accurate."
msgstr "Het minimale formaat van een bewegingslijnsegment na het slicen. Als u deze waarde verhoogt, hebben de bewegingen minder vloeiende hoeken. Hiermee kan de printer de verwerkingssnelheid van de G-code bijhouden, maar kan het model door vermijding minder nauwkeurig worden."

msgctxt "support_bottom_stair_step_min_slope description"
msgid "The minimum slope of the area for stair-stepping to take effect. Low values should make support easier to remove on shallower slopes, but really low values may result in some very counter-intuitive results on other parts of the model."
msgstr "De minimale helling van het gebied voordat traptreden van kracht worden. Lage waarden zouden het gemakkelijker moeten maken om support op ondieperere hellingen te verwijderen. Zeer lage waarden kunnen echter resulteren in een aantal zeer contra-intuïtieve resultaten op andere delen van het model."

msgctxt "cool_min_layer_time description"
msgid "The minimum time spent in a layer. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr "De tijd die minimaal aan het printen van een laag wordt besteed. Hierdoor wordt de printer gedwongen langzamer te printen zodat deze ten minste de ingestelde tijd gebruikt voor het printen van een laag. Hierdoor kan het geprinte materiaal voldoende afkoelen voordat de volgende laag wordt geprint. Het printen van lagen kan nog steeds minder lang duren dan de minimale laagtijd als Printkop optillen is uitgeschakeld en als anders niet zou worden voldaan aan de Minimumsnelheid."

msgctxt "prime_tower_min_volume description"
msgid "The minimum volume for each layer of the prime tower in order to purge enough material."
msgstr "Het minimale volume voor elke laag van de primepijler om voldoende materiaal te zuiveren."

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model description"
msgid "The most the diameter of a branch that has to connect to the model may increase by merging with branches that could reach the buildplate. Increasing this reduces print time, but increases the area of support that rests on model"
msgstr "De diameter van een tak die moet aansluiten op het model mag maximaal toenemen door samen te voegen met takken die de bouwplaat zouden kunnen bereiken. Als u dit verhoogt, wordt de printtijd verkort, maar wordt het ondersteuningsgebied dat op het model rust vergroot"

msgctxt "machine_name description"
msgid "The name of your 3D printer model."
msgstr "De naam van uw 3D-printermodel."

msgctxt "machine_nozzle_id description"
msgid "The nozzle ID for an extruder train, such as \"AA 0.4\" and \"BB 0.8\"."
msgstr "De nozzle-ID voor een extruder train, bijvoorbeeld \"AA 0.4\" en \"BB 0.8\"."

msgctxt "travel_avoid_other_parts description"
msgid "The nozzle avoids already printed parts when traveling. This option is only available when combing is enabled."
msgstr "Tijdens bewegingen mijdt de nozzle delen die al zijn geprint. Deze optie is alleen beschikbaar wanneer combing ingeschakeld is."

msgctxt "travel_avoid_supports description"
msgid "The nozzle avoids already printed supports when traveling. This option is only available when combing is enabled."
msgstr "Tijdens bewegingen mijdt de nozzle supportstructuren die al zijn geprint. Deze optie is alleen beschikbaar wanneer combing ingeschakeld is."

msgctxt "bottom_layers description"
msgid "The number of bottom layers. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "Het aantal bodemlagen. Wanneer deze waarde wordt berekend aan de hand van de dikte van de bodem, wordt deze afgerond naar een geheel getal."

msgctxt "raft_base_wall_count description"
msgid "The number of contours to print around the linear pattern in the base layer of the raft."
msgstr "Het aantal contouren dat wordt geprint rond het lineaire patroon in de basislaag van de raft."

msgctxt "raft_interface_wall_count description"
msgid "The number of contours to print around the linear pattern in the middle layers of the raft."
msgstr "Het aantal contouren dat moet worden geprint rond het lineaire patroon in de middelste lagen van het vlot."

msgctxt "raft_surface_wall_count description"
msgid "The number of contours to print around the linear pattern in the top layers of the raft."
msgstr "Het aantal contouren dat moet worden geprint rond het lineaire patroon in de bovenste lagen van het vlot."

msgctxt "raft_wall_count description"
msgid "The number of contours to print around the linear pattern of the raft."
msgstr "Het aantal contouren dat moet worden geprint rond het lineaire patroon van het vlot."

msgctxt "skin_edge_support_layers description"
msgid "The number of infill layers that supports skin edges."
msgstr "Het aantal opvullagen dat skinranden ondersteunt."

msgctxt "initial_bottom_layers description"
msgid "The number of initial bottom layers, from the build-plate upwards. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "Het aantal initiële onderste lagen, vanaf de bouwplaat naar boven. Wanneer deze waarde wordt berekend aan de hand van de dikte van de bodem, wordt deze afgerond naar een geheel getal."

msgctxt "raft_interface_layers description"
msgid "The number of layers between the base and the surface of the raft. These comprise the main thickness of the raft. Increasing this creates a thicker, sturdier raft."
msgstr "Het aantal lagen tussen de basis en het oppervlak van de raft. Deze omvatten de het grootste deel van de dikte van de raft. Uitbreiden hiervan creëert een dikkere, stevigere raft."

msgctxt "brim_line_count description"
msgid "The number of lines used for a brim. More brim lines enhance adhesion to the build plate, but also reduces the effective print area."
msgstr "Het aantal lijnen dat voor een brim wordt gebruikt. Meer lijnen zorgen voor betere hechting aan het platform, maar verkleinen uw effectieve printgebied."

msgctxt "support_brim_line_count description"
msgid "The number of lines used for the support brim. More brim lines enhance adhesion to the build plate, at the cost of some extra material."
msgstr "Het aantal lijnen dat voor de supportbrim wordt gebruikt. Meer brimlijnen zorgen voor betere hechting aan het platform, maar kosten wat extra materiaal."

msgctxt "raft_surface_layers description"
msgid "The number of top layers on top of the 2nd raft layer. These are fully filled layers that the model sits on. 2 layers result in a smoother top surface than 1."
msgstr "Het aantal bovenlagen op de tweede raftlaag. Dit zijn volledig gevulde lagen waarop het model rust. Met twee lagen krijgt u een gladder oppervlak dan met één laag."

msgctxt "top_layers description"
msgid "The number of top layers. When calculated by the top thickness, this value is rounded to a whole number."
msgstr "Het aantal bovenlagen. Wanneer deze waarde wordt berekend aan de hand van de dikte van de bovenkant, wordt deze afgerond naar een geheel getal."

msgctxt "roofing_layer_count description"
msgid "The number of top most skin layers. Usually only one top most layer is sufficient to generate higher quality top surfaces."
msgstr "Het aantal bovenste skinlagen. Doorgaans is één bovenste skinlaag voldoende om oppervlakken van hogere kwaliteit te verkrijgen."

msgctxt "support_wall_count description"
msgid "The number of walls with which to surround support infill. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Het aantal wanden rond de vulling van de supportstructuur. Met een extra wand wordt de supportstructuur betrouwbaarder en kan de overhang beter worden geprint, maar wordt de printtijd verlengd en wordt meer materiaal gebruikt."

msgctxt "support_bottom_wall_count description"
msgid "The number of walls with which to surround support interface floor. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Het aantal muren waarmee de ondersteuningsinterfacevloer moet worden omgeven. Door een muur toe te voegen, kan de ondersteuningsprint betrouwbaarder worden gemaakt en kunnen overhangen beter worden ondersteund, maar neemt de printtijd en het gebruikte materiaal toe."

msgctxt "support_roof_wall_count description"
msgid "The number of walls with which to surround support interface roof. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Het aantal muren waarmee het ondersteuningsinterfacedak kan worden omgeven. Door een muur toe te voegen, kan de ondersteuningsprint betrouwbaarder worden gemaakt en kunnen overhangen beter worden ondersteund, maar neemt de printtijd en het gebruikte materiaal toe."

msgctxt "support_interface_wall_count description"
msgid "The number of walls with which to surround support interface. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Het aantal muren waarmee de ondersteuningsinterface moet worden omgeven. Door een muur toe te voegen, kan de ondersteuningsprint betrouwbaarder worden gemaakt en kunnen overhangen beter worden ondersteund, maar neemt de printtijd en het gebruikte materiaal toe."

msgctxt "wall_distribution_count description"
msgid "The number of walls, counted from the center, over which the variation needs to be spread. Lower values mean that the outer walls don't change in width."
msgstr "Het aantal wanden, geteld vanaf het midden, waarover de variatie moet worden gespreid. Lagere waarden betekenen dat de breedte van de buitenwanden niet verandert."

msgctxt "wall_line_count description"
msgid "The number of walls. When calculated by the wall thickness, this value is rounded to a whole number."
msgstr "Het aantal wandlijnen. Wanneer deze waarde wordt berekend aan de hand van de wanddikte, wordt deze afgerond naar een geheel getal."

msgctxt "machine_nozzle_tip_outer_diameter description"
msgid "The outer diameter of the tip of the nozzle."
msgstr "De buitendiameter van de punt van de nozzle."

msgctxt "infill_pattern description"
msgid "The pattern of the infill material of the print. The line and zig zag infill swap direction on alternate layers, reducing material cost. The grid, triangle, tri-hexagon, cubic, octet, quarter cubic, cross and concentric patterns are fully printed every layer. Gyroid, cubic, quarter cubic and octet infill change with every layer to provide a more equal distribution of strength over each direction. Lightning infill tries to minimize the infill, by only supporting the ceiling of the object."
msgstr "Het patroon van het vulmateriaal van de print. De lijn- en zigzagvulling veranderen per vullaag van richting, waardoor u bespaart op materiaalkosten. De raster-, driehoeks-, tri-hexagonale, kubische, achtvlaks-, afgeknotte kubus-, kruis- en concentrische patronen worden per laag volledig geprint. Gyroïde, kubische, afgeknotte kubus- en achtvlaksvullingen veranderen per laag voor een meer gelijke krachtverdeling in elke richting. Bliksemvulling minimaliseert de vulling doordat deze alleen het plafond van het object ondersteunt."

msgctxt "support_pattern description"
msgid "The pattern of the support structures of the print. The different options available result in sturdy or easy to remove support."
msgstr "Het patroon van de supportstructuur van de print. Met de verschillende beschikbare opties print u stevige of eenvoudig te verwijderen supportstructuren."

msgctxt "roofing_pattern description"
msgid "The pattern of the top most layers."
msgstr "Het patroon van de bovenste lagen."

msgctxt "top_bottom_pattern description"
msgid "The pattern of the top/bottom layers."
msgstr "Het patroon van de boven-/onderlagen."

msgctxt "top_bottom_pattern_0 description"
msgid "The pattern on the bottom of the print on the first layer."
msgstr "Het patroon van de eerste laag aan de onderkant van de print."

msgctxt "ironing_pattern description"
msgid "The pattern to use for ironing top surfaces."
msgstr "Het patroon dat wordt gebruikt voor het strijken van oppervlakken."

msgctxt "support_bottom_pattern description"
msgid "The pattern with which the floors of the support are printed."
msgstr "Het patroon waarmee de vloeren van de supportstructuur worden geprint."

msgctxt "support_interface_pattern description"
msgid "The pattern with which the interface of the support with the model is printed."
msgstr "Het patroon waarmee de verbindingsstructuur van het model wordt geprint."

msgctxt "support_roof_pattern description"
msgid "The pattern with which the roofs of the support are printed."
msgstr "Het patroon waarmee de daken van de supportstructuur worden geprint."

msgctxt "z_seam_position description"
msgid "The position near where to start printing each part in a layer."
msgstr "De positie nabij waar met het printen van elk deel van een laag moet worden begonnen."

msgctxt "support_tree_angle_slow description"
msgid "The preferred angle of the branches, when they do not have to avoid the model. Use a lower angle to make them more vertical and more stable. Use a higher angle for branches to merge faster."
msgstr "De geprefereerde hoek van de takken, wanneer ze het model niet hoeven te vermijden. Gebruik een lagere hoek om ze verticaler en stabieler te maken. Gebruik een hogere hoek voor takken om sneller samen te voegen."

msgctxt "support_tree_rest_preference description"
msgid "The preferred placement of the support structures. If structures can't be placed at the preferred location, they will be place elsewhere, even if that means placing them on the model."
msgstr "De voorkeursplaats van de ondersteunende structuren. Als structuren niet op de gewenste locatie kunnen worden geplaatst, worden ze elders geplaatst, zelfs als dat betekent dat ze op het model moeten worden geplaatst."

msgctxt "jerk_layer_0 description"
msgid "The print maximum instantaneous velocity change for the initial layer."
msgstr "De maximale onmiddellijke snelheidsverandering in de eerste laag."

msgctxt "machine_shape description"
msgid "The shape of the build plate without taking unprintable areas into account."
msgstr "De vorm van het platform zonder rekening te houden met niet-printbare gebieden."

msgctxt "machine_head_with_fans_polygon description"
msgid "The shape of the print head. These are coordinates relative to the position of the print head, which is usually the position of its first extruder. The dimensions left and in front of the print head must be negative coordinates."
msgstr "De vorm van de printkop. Deze coördinaten hebben betrekking op de positie van de printkop. Meestal is dit de positie van de eerste extruder. De dimensies links van en vóór de printkop moeten negatieve coördinaten zijn."

msgctxt "cross_infill_pocket_size description"
msgid "The size of pockets at four-way crossings in the cross 3D pattern at heights where the pattern is touching itself."
msgstr "De grootte van luchtbellen op kruispunten in het kruis 3D-patroon op punten waar het patroon zichzelf raakt."

msgctxt "coasting_min_volume description"
msgid "The smallest volume an extrusion path should have before allowing coasting. For smaller extrusion paths, less pressure has been built up in the bowden tube and so the coasted volume is scaled linearly. This value should always be larger than the Coasting Volume."
msgstr "Het kleinste volume dat een doorvoerpad moet hebben, voordat coasting mogelijk is. Voor een kort doorvoerpad wordt in de Bowden-buis minder druk opgebouwd en wordt het uitgespreide volume daarom lineair geschaald. Deze waarde moet altijd groter zijn dan de waarde voor het coasting-volume."

msgctxt "machine_nozzle_cool_down_speed description"
msgid "The speed (°C/s) by which the nozzle cools down averaged over the window of normal printing temperatures and the standby temperature."
msgstr "De snelheid (°C/s) waarmee de nozzle afkoelt, gemiddeld over het venster van normale printtemperaturen en de stand-bytemperatuur."

msgctxt "machine_nozzle_heat_up_speed description"
msgid "The speed (°C/s) by which the nozzle heats up averaged over the window of normal printing temperatures and the standby temperature."
msgstr "De snelheid (°C/s) waarmee de nozzle wordt verwarmd, gemiddeld over het venster van normale printtemperaturen en de stand-bytemperatuur."

msgctxt "speed_wall_x description"
msgid "The speed at which all inner walls are printed. Printing the inner wall faster than the outer wall will reduce printing time. It works well to set this in between the outer wall speed and the infill speed."
msgstr "De snelheid waarmee alle binnenwanden worden geprint. Als u de binnenwand sneller print dan de buitenwand, verkort u de printtijd. Het wordt aangeraden hiervoor een snelheid in te stellen die ligt tussen de printsnelheid van de buitenwand en de vulsnelheid."

msgctxt "bridge_skin_speed description"
msgid "The speed at which bridge skin regions are printed."
msgstr "De snelheid waarmee brugskinregio's worden geprint."

msgctxt "speed_infill description"
msgid "The speed at which infill is printed."
msgstr "De snelheid waarmee de vulling wordt geprint."

msgctxt "speed_print description"
msgid "The speed at which printing happens."
msgstr "De snelheid waarmee wordt geprint."

msgctxt "raft_base_speed description"
msgid "The speed at which the base raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "De snelheid waarmee de grondlaag van de raft wordt geprint. Deze laag moet vrij langzaam worden geprint, omdat er vrij veel materiaal uit de nozzle komt."

msgctxt "bridge_wall_speed description"
msgid "The speed at which the bridge walls are printed."
msgstr "De snelheid waarmee brugwanden worden geprint."

msgctxt "cool_fan_speed_0 description"
msgid "The speed at which the fans spin at the start of the print. In subsequent layers the fan speed is gradually increased up to the layer corresponding to Regular Fan Speed at Height."
msgstr "De snelheid waarmee de ventilatoren draaien bij de start van het printen. Tijdens het printen van de volgende lagen wordt de ventilatorsnelheid geleidelijk verhoogd tot de laag waarin de snelheid overeenkomt met de normale ventilatorsnelheid op hoogte."

msgctxt "cool_fan_speed_min description"
msgid "The speed at which the fans spin before hitting the threshold. When a layer prints faster than the threshold, the fan speed gradually inclines towards the maximum fan speed."
msgstr "De snelheid waarmee de ventilatoren draaien voordat de drempelwaarde wordt bereikt. Wanneer een laag sneller wordt geprint dan de drempelwaarde, wordt de ventilatorsnelheid geleidelijk verhoogd tot de maximale ventilatorsnelheid."

msgctxt "cool_fan_speed_max description"
msgid "The speed at which the fans spin on the minimum layer time. The fan speed gradually increases between the regular fan speed and maximum fan speed when the threshold is hit."
msgstr "De snelheid waarmee de ventilatoren draaien bij de minimale laagtijd. Wanneer de drempelwaarde wordt bereikt, wordt de ventilatorsnelheid geleidelijk verhoogd van de normale ventilatorsnelheid naar de maximale ventilatorsnelheid."

msgctxt "retraction_prime_speed description"
msgid "The speed at which the filament is primed during a retraction move."
msgstr "De snelheid waarmee het filament tijdens een intrekbeweging wordt geprimed."

msgctxt "wipe_retraction_prime_speed description"
msgid "The speed at which the filament is primed during a wipe retraction move."
msgstr "De snelheid waarmee het filament tijdens een intrekbeweging voor afvegen wordt geprimed."

msgctxt "switch_extruder_prime_speed description"
msgid "The speed at which the filament is pushed back after a nozzle switch retraction."
msgstr "De snelheid waarmee het filament tijdens een intrekbeweging na het wisselen van de nozzles wordt geprimed."

msgctxt "retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a retraction move."
msgstr "De snelheid waarmee het filament tijdens een intrekbeweging wordt ingetrokken en geprimed."

msgctxt "wipe_retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a wipe retraction move."
msgstr "De snelheid waarmee het filament tijdens een intrekbeweging voor afvegen wordt ingetrokken en geprimed."

msgctxt "switch_extruder_retraction_speed description"
msgid "The speed at which the filament is retracted during a nozzle switch retract."
msgstr "De snelheid waarmee het filament tijdens een intrekbeweging tijdens het wisselen van de nozzles wordt ingetrokken."

msgctxt "retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a retraction move."
msgstr "De snelheid waarmee het filament tijdens een intrekbeweging wordt ingetrokken."

msgctxt "wipe_retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a wipe retraction move."
msgstr "De snelheid waarmee het filament tijdens een intrekbeweging voor afvegen wordt ingetrokken."

msgctxt "switch_extruder_retraction_speeds description"
msgid "The speed at which the filament is retracted. A higher retraction speed works better, but a very high retraction speed can lead to filament grinding."
msgstr "De snelheid waarmee het filament wordt ingetrokken. Een hogere intreksnelheid werkt beter, maar bij een erg hoge intreksnelheid kan het filament gaan haperen."

msgctxt "speed_support_bottom description"
msgid "The speed at which the floor of support is printed. Printing it at lower speed can improve adhesion of support on top of your model."
msgstr "De snelheid waarmee de supportvloer wordt geprint. Als u deze langzamer print, hecht het supportmateriaal beter aan de bovenzijde van het model."

msgctxt "speed_support_infill description"
msgid "The speed at which the infill of support is printed. Printing the infill at lower speeds improves stability."
msgstr "De snelheid waarmee de supportvulling wordt geprint. Als u de vulling langzamer print, wordt de stabiliteit verbeterd."

msgctxt "raft_interface_speed description"
msgid "The speed at which the middle raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "De snelheid waarmee de middelste laag van de raft wordt geprint. Deze laag moet vrij langzaam worden geprint, omdat er vrij veel materiaal uit de nozzle komt."

msgctxt "speed_wall_0 description"
msgid "The speed at which the outermost walls are printed. Printing the outer wall at a lower speed improves the final skin quality. However, having a large difference between the inner wall speed and the outer wall speed will affect quality in a negative way."
msgstr "De snelheid waarmee de buitenwanden worden geprint. Als u de buitenwand langzamer print, verhoogt u de uiteindelijke kwaliteit van de skin. Een groot verschil tussen de printsnelheid van de binnenwand en de printsnelheid van de buitenwand kan echter een negatief effect hebben op de kwaliteit."

msgctxt "speed_prime_tower description"
msgid "The speed at which the prime tower is printed. Printing the prime tower slower can make it more stable when the adhesion between the different filaments is suboptimal."
msgstr "De snelheid waarmee de primepijler wordt geprint. Als u de primepijler langzamer print, wordt deze stabieler. Dit is zinvol wanneer de hechting tussen de verschillende filamenten niet optimaal is."

msgctxt "cool_fan_speed description"
msgid "The speed at which the print cooling fans spin."
msgstr "De snelheid waarmee de printventilatoren draaien."

msgctxt "raft_speed description"
msgid "The speed at which the raft is printed."
msgstr "De snelheid waarmee de raft wordt geprint."

msgctxt "speed_support_interface description"
msgid "The speed at which the roofs and floors of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "De snelheid waarmee de supportdaken en -vloeren worden geprint. Als u deze langzamer print, wordt de kwaliteit van de overhang verbeterd."

msgctxt "speed_support_roof description"
msgid "The speed at which the roofs of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "De snelheid waarmee de supportdaken worden geprint. Als u deze langzamer print, wordt de kwaliteit van de overhang verbeterd."

msgctxt "skirt_brim_speed description"
msgid "The speed at which the skirt and brim are printed. Normally this is done at the initial layer speed, but sometimes you might want to print the skirt or brim at a different speed."
msgstr "De snelheid waarmee de skirt en de brim worden geprint. Normaal gebeurt dit met dezelfde snelheid als de snelheid van de eerste laag, maar in sommige situaties wilt u de skirt of de brim mogelijk met een andere snelheid printen."

msgctxt "speed_support description"
msgid "The speed at which the support structure is printed. Printing support at higher speeds can greatly reduce printing time. The surface quality of the support structure is not important since it is removed after printing."
msgstr "De snelheid waarmee de supportstructuur wordt geprint. Als u de supportstructuur sneller print, kunt u de printtijd aanzienlijk verkorten. De kwaliteit van het oppervlak van de supportstructuur is niet belangrijk, aangezien deze na het printen wordt verwijderd."

msgctxt "raft_surface_speed description"
msgid "The speed at which the top raft layers are printed. These should be printed a bit slower, so that the nozzle can slowly smooth out adjacent surface lines."
msgstr "De snelheid waarmee de toplagen van de raft worden geprint. Deze lagen moeten iets langzamer worden geprint, zodat de nozzle de aangrenzende oppervlaktelijnen langzaam kan effenen."

msgctxt "speed_wall_x_roofing description"
msgid "The speed at which the top surface inner walls are printed."
msgstr "De snelheid waarmee de binnenwanden van het bovenoppervlak worden geprint."

msgctxt "speed_wall_0_roofing description"
msgid "The speed at which the top surface outermost wall is printed."
msgstr "De snelheid waarmee de buitenste wanden van het bovenoppervlak worden geprint."

msgctxt "speed_z_hop description"
msgid "The speed at which the vertical Z movement is made for Z Hops. This is typically lower than the print speed since the build plate or machine's gantry is harder to move."
msgstr "De snelheid waarmee de verticale Z-beweging wordt gemaakt voor Z-sprongen. Dit is meestal lager dan de printsnelheid, omdat het platform of de rijbrug van de machine moeilijker te verplaatsen is."

msgctxt "speed_wall description"
msgid "The speed at which the walls are printed."
msgstr "De snelheid waarmee wanden worden geprint."

msgctxt "speed_ironing description"
msgid "The speed at which to pass over the top surface."
msgstr "De snelheid waarmee over de bovenste laag wordt bewogen."

msgctxt "material_break_speed description"
msgid "The speed at which to retract the filament in order to break it cleanly."
msgstr "De snelheid waarmee het filament wordt ingetrokken om het recht af te breken."

msgctxt "speed_roofing description"
msgid "The speed at which top surface skin layers are printed."
msgstr "De snelheid waarmee bovenste skinlagen worden geprint."

msgctxt "speed_topbottom description"
msgid "The speed at which top/bottom layers are printed."
msgstr "De snelheid waarmee boven-/onderlagen worden geprint."

msgctxt "speed_travel description"
msgid "The speed at which travel moves are made."
msgstr "De snelheid waarmee bewegingen plaatsvinden."

msgctxt "coasting_speed description"
msgid "The speed by which to move during coasting, relative to the speed of the extrusion path. A value slightly under 100% is advised, since during the coasting move the pressure in the bowden tube drops."
msgstr "De snelheid waarmee de printkop tijdens coasting beweegt ten opzichte van de snelheid voor het doorvoerpad. Hiervoor wordt een waarde van iets minder dan 100% aangeraden, omdat de druk in de bowden-buis zakt tijdens een coasting-beweging."

msgctxt "speed_layer_0 description"
msgid "The speed for the initial layer. A lower value is advised to improve adhesion to the build plate. Does not affect the build plate adhesion structures themselves, like brim and raft."
msgstr "De snelheid waarmee de eerste laag wordt geprint. Hiervoor wordt een lagere waarde aanbevolen om hechting aan het platform te verbeteren. Heeft geen invloed op de hechtstructuren van het platform zelf, zoals brim en raft."

msgctxt "speed_print_layer_0 description"
msgid "The speed of printing for the initial layer. A lower value is advised to improve adhesion to the build plate."
msgstr "De snelheid waarmee de eerste laag wordt geprint. Hiervoor wordt een lagere waarde aanbevolen om hechting aan het platform te verbeteren."

msgctxt "speed_travel_layer_0 description"
msgid "The speed of travel moves in the initial layer. A lower value is advised to prevent pulling previously printed parts away from the build plate. The value of this setting can automatically be calculated from the ratio between the Travel Speed and the Print Speed."
msgstr "De snelheid van de bewegingen tijdens het printen van de eerste laag. Hiervoor wordt een lagere waarde aanbevolen om te voorkomen dat eerder geprinte delen van het platform worden getrokken. De waarde van deze instelling kan automatisch worden berekend uit de verhouding tussen de bewegingssnelheid en de printsnelheid."

msgctxt "material_break_temperature description"
msgid "The temperature at which the filament is broken for a clean break."
msgstr "De temperatuur waarbij het filament wordt afgebroken om het recht af te breken."

msgctxt "build_volume_temperature description"
msgid "The temperature of the environment to print in. If this is 0, the build volume temperature will not be adjusted."
msgstr "De omgevingstemperatuur waarin wordt geprint. Als deze waarde is ingesteld op 0, wordt de temperatuur van het werkvolume niet aangepast."

msgctxt "material_standby_temperature description"
msgid "The temperature of the nozzle when another nozzle is currently used for printing."
msgstr "De temperatuur van de nozzle op de momenten waarop een andere nozzle wordt gebruikt voor het printen."

msgctxt "material_final_print_temperature description"
msgid "The temperature to which to already start cooling down just before the end of printing."
msgstr "De temperatuur waarnaar alvast kan worden afgekoeld net voordat het printen wordt beëindigd."

msgctxt "material_print_temperature_layer_0 description"
msgid "The temperature used for printing the first layer."
msgstr "De temperatuur die gebruikt wordt voor het printen van de eerste laag."

msgctxt "material_print_temperature description"
msgid "The temperature used for printing."
msgstr "De temperatuur waarmee wordt geprint."

msgctxt "material_bed_temperature_layer_0 description"
msgid "The temperature used for the heated build plate at the first layer. If this is 0, the build plate is left unheated during the first layer."
msgstr "De temperatuur van het verwarmde platform voor de eerste laag. Als de temperatuur 0 is, wordt het platform bij de eerste laag niet verwarmd."

msgctxt "material_bed_temperature description"
msgid "The temperature used for the heated build plate. If this is 0, the build plate is left unheated."
msgstr "De temperatuur van het verwarmde platform. Als de temperatuur is ingesteld op 0, wordt het platform niet verwarmd."

msgctxt "material_break_preparation_temperature description"
msgid "The temperature used to purge material, should be roughly equal to the highest possible printing temperature."
msgstr "De temperatuur die wordt gebruikt om materiaal te zuiveren, moet ongeveer gelijk zijn aan de hoogst mogelijke printtemperatuur."

msgctxt "bottom_thickness description"
msgid "The thickness of the bottom layers in the print. This value divided by the layer height defines the number of bottom layers."
msgstr "De dikte van de onderlagen in de print. Het aantal onderlagen wordt bepaald door het delen van deze waarde door de laaghoogte."

msgctxt "skin_edge_support_thickness description"
msgid "The thickness of the extra infill that supports skin edges."
msgstr "De dikte van de extra vulling die skinranden ondersteunt."

msgctxt "support_interface_height description"
msgid "The thickness of the interface of the support where it touches with the model on the bottom or the top."
msgstr "De dikte van de verbindingsstructuur waar dit het model aan de onder- of bovenkant raakt."

msgctxt "support_bottom_height description"
msgid "The thickness of the support floors. This controls the number of dense layers that are printed on top of places of a model on which support rests."
msgstr "De dikte van de supportvloeren. Hiermee wordt het aantal dichte lagen bepaald dat wordt geprint op plekken van een model waarop een supportstructuur rust."

msgctxt "support_roof_height description"
msgid "The thickness of the support roofs. This controls the amount of dense layers at the top of the support on which the model rests."
msgstr "De dikte van de supportdaken. Hiermee wordt het aantal dichte lagen bepaald aan de bovenkant van de supportstructuur waarop het model rust."

msgctxt "top_thickness description"
msgid "The thickness of the top layers in the print. This value divided by the layer height defines the number of top layers."
msgstr "De dikte van de bovenlagen in de print. Het aantal bovenlagen wordt bepaald door het delen van deze waarde door de laaghoogte."

msgctxt "top_bottom_thickness description"
msgid "The thickness of the top/bottom layers in the print. This value divided by the layer height defines the number of top/bottom layers."
msgstr "De dikte van de boven-/onderlagen in de print. Het aantal boven-/onderlagen wordt bepaald door het delen van deze waarde door de laaghoogte."

msgctxt "wall_thickness description"
msgid "The thickness of the walls in the horizontal direction. This value divided by the wall line width defines the number of walls."
msgstr "De dikte van de wanden in horizontale richting. Het aantal wanden wordt bepaald door het delen van deze waarde door de breedte van de wandlijn."

msgctxt "infill_sparse_thickness description"
msgid "The thickness per layer of infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "De dikte per laag vulmateriaal. Deze waarde moet altijd een veelvoud van de laaghoogte zijn en wordt voor het overige afgerond."

msgctxt "support_infill_sparse_thickness description"
msgid "The thickness per layer of support infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "De dikte per laag materiaal supportvulling. Deze waarde moet altijd een veelvoud van de laaghoogte zijn en wordt voor het overige afgerond."

msgctxt "machine_gcode_flavor description"
msgid "The type of g-code to be generated."
msgstr "De G-code-versie die moet worden gegenereerd."

msgctxt "material_type description"
msgid "The type of material used."
msgstr "Het type materiaal dat gebruikt wordt."

msgctxt "coasting_volume description"
msgid "The volume otherwise oozed. This value should generally be close to the nozzle diameter cubed."
msgstr "Hiermee stelt u volume in dat anders zou worden afgevoerd. Deze waarde dient zo dicht mogelijk bij de berekende waarde van de nozzlediameter te liggen."

msgctxt "machine_width description"
msgid "The width (X-direction) of the printable area."
msgstr "De breedte (X-richting) van het printbare gebied."

msgctxt "support_brim_width description"
msgid "The width of the brim to print underneath the support. A larger brim enhances adhesion to the build plate, at the cost of some extra material."
msgstr "De breedte van de brim die onder de support wordt geprint. Een bredere brim kost meer materiaal, maar hecht beter aan het platform."

msgctxt "interlocking_beam_width description"
msgid "The width of the interlocking structure beams."
msgstr "De breedte van de in elkaar grijpende structuurbalken."

msgctxt "prime_tower_base_size description"
msgid "The width of the prime tower brim/base. A larger base enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "De breedte van de brim/basis van de prime toren. Een grotere basis verbetert de hechting aan het bouwplateau, maar vermindert ook het effectieve printgebied."

msgctxt "prime_tower_size description"
msgid "The width of the prime tower."
msgstr "De breedte van de primepijler."

msgctxt "magic_fuzzy_skin_thickness description"
msgid "The width within which to jitter. It's advised to keep this below the outer wall width, since the inner walls are unaltered."
msgstr "De breedte van de trilling. Het wordt aangeraden hiervoor een waarde in te stellen die lager is dan de breedte van de buitenwand, omdat de binnenwand niet verandert."

msgctxt "retraction_extrusion_window description"
msgid "The window in which the maximum retraction count is enforced. This value should be approximately the same as the retraction distance, so that effectively the number of times a retraction passes the same patch of material is limited."
msgstr "Dit is het gebied waarop het maximaal aantal intrekbewegingen van toepassing is. Deze waarde moet ongeveer overeenkomen met de Intrekafstand, waarmee in feite het aantal intrekbewegingen op hetzelfde deel van het materiaal wordt beperkt."

msgctxt "prime_tower_position_x description"
msgid "The x coordinate of the position of the prime tower."
msgstr "De X-coördinaat van de positie van de primepijler."

msgctxt "prime_tower_position_y description"
msgid "The y coordinate of the position of the prime tower."
msgstr "De Y-coördinaat van de positie van de primepijler."

msgctxt "support_meshes_present description"
msgid "There are support meshes present in the scene. This setting is controlled by Cura."
msgstr "Er zijn supportrasters aanwezig in de scène. Deze instelling wordt beheerd door Cura."

msgctxt "bridge_wall_coast description"
msgid "This controls the distance the extruder should coast immediately before a bridge wall begins. Coasting before the bridge starts can reduce the pressure in the nozzle and may produce a flatter bridge."
msgstr "Met deze optie controleert u de afstand die de extruder moet coasten voordat een brugwand begint. Met coasting voordat de brug begint, vermindert u de druk in de nozzle en krijgt u mogelijk een vlakkere brug."

msgctxt "raft_base_smoothing description"
msgid "This setting controls how much inner corners in the raft base outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Deze instelling bepaalt hoeveel binnenhoeken in de omtrek van het basisvlot worden afgerond. Binnenhoeken worden afgerond tot een halve cirkel met een straal gelijk aan de hier opgegeven waarde. Deze instelling verwijdert ook gaten in de omtrek van het vlot die kleiner zijn dan zo'n cirkel."

msgctxt "raft_interface_smoothing description"
msgid "This setting controls how much inner corners in the raft middle outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Deze instelling bepaalt hoeveel binnenhoeken in de omtrek van het midden van het vlot worden afgerond. Binnenhoeken worden afgerond tot een halve cirkel met een straal gelijk aan de hier opgegeven waarde. Deze instelling verwijdert ook gaten in de omtrek van het vlot die kleiner zijn dan zo'n cirkel."

msgctxt "raft_smoothing description"
msgid "This setting controls how much inner corners in the raft outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Bepaalt hoeveel binnenhoeken in de raftcontour worden afgerond. Naar binnen gebogen hoeken worden tot een halve cirkel afgerond met een straal die gelijk is aan de hier opgegeven waarde. Met deze instellingen worden ook gaten in de raftcontour verwijderd die kleiner zijn dan een dergelijke cirkel."

msgctxt "raft_surface_smoothing description"
msgid "This setting controls how much inner corners in the raft top outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Deze instelling bepaalt hoeveel binnenhoeken in de omtrek van de bovenkant van het vlot worden afgerond. Binnenhoeken worden afgerond tot een halve cirkel met een straal gelijk aan de hier opgegeven waarde. Deze instelling verwijdert ook gaten in de omtrek van het vlot die kleiner zijn dan zo'n cirkel."

msgctxt "retraction_count_max description"
msgid "This setting limits the number of retractions occurring within the minimum extrusion distance window. Further retractions within this window will be ignored. This avoids retracting repeatedly on the same piece of filament, as that can flatten the filament and cause grinding issues."
msgstr "Deze instelling beperkt het aantal intrekbewegingen dat kan worden uitgevoerd binnen het gebied Minimaal afstandsgebied voor intrekken. Extra intrekbewegingen binnen dit gebied worden genegeerd. Hiermee voorkomt u dat hetzelfde stuk filament meerdere keren wordt ingetrokken en dus kan worden geplet en kan gaan haperen."

msgctxt "draft_shield_enabled description"
msgid "This will create a wall around the model, which traps (hot) air and shields against exterior airflow. Especially useful for materials which warp easily."
msgstr "Maak een wand rond het model. Deze vangt (warme) lucht en biedt bescherming tegen externe luchtbewegingen. De optie is met name geschikt voor materialen die snel kromtrekken."

msgctxt "support_tree_tip_diameter label"
msgid "Tip Diameter"
msgstr "Puntdiameter"

msgctxt "material_shrinkage_percentage_xy description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the XY-direction (horizontally)."
msgstr "Om te compenseren voor het krimpen van het materiaal wanneer het afkoelt, wordt het model met deze factor geschaald in de richting XY (horizontaal)."

msgctxt "material_shrinkage_percentage_z description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the Z-direction (vertically)."
msgstr "Om te compenseren voor het krimpen van het materiaal wanneer het afkoelt, wordt het model met deze factor geschaald in Z-richting (verticaal)."

msgctxt "material_shrinkage_percentage description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor."
msgstr "Het model wordt met deze factor geschaald ter compensatie van het krimpen van het materiaal tijdens het afkoelen."

msgctxt "top_layers label"
msgid "Top Layers"
msgstr "Bovenlagen"

msgctxt "top_skin_expand_distance label"
msgid "Top Skin Expand Distance"
msgstr "Uitbreidingsafstand van bovenste skinlaag"

msgctxt "top_skin_preshrink label"
msgid "Top Skin Removal Width"
msgstr "Verwijderingsbreedte bovenste skinlaag"

msgctxt "acceleration_wall_x_roofing label"
msgid "Top Surface Inner Wall Acceleration"
msgstr "Versnelling van de binnenwand op bovenlaag"

msgctxt "jerk_wall_x_roofing label"
msgid "Top Surface Inner Wall Jerk"
msgstr "Schok van de buitenste muur van het bovenoppervlak"

msgctxt "speed_wall_x_roofing label"
msgid "Top Surface Inner Wall Speed"
msgstr "Snelheid van de binnenste wand van het bovenoppervlak"

msgctxt "wall_x_material_flow_roofing label"
msgid "Top Surface Inner Wall(s) Flow"
msgstr "Stroom van de binnenste wand(en) van het bovenoppervlak"

msgctxt "acceleration_wall_0_roofing label"
msgid "Top Surface Outer Wall Acceleration"
msgstr "Versnelling van de buitenste wand op bovenlaag"

msgctxt "wall_0_material_flow_roofing label"
msgid "Top Surface Outer Wall Flow"
msgstr "Stroom van de buitenste wand van het bovenoppervlak"

msgctxt "jerk_wall_0_roofing label"
msgid "Top Surface Outer Wall Jerk"
msgstr "Schok van de binnenste muur van het bovenoppervlak"

msgctxt "speed_wall_0_roofing label"
msgid "Top Surface Outer Wall Speed"
msgstr "Snelheid van de buitenste wand van het bovenoppervlak"

msgctxt "acceleration_roofing label"
msgid "Top Surface Skin Acceleration"
msgstr "Acceleratie bovenskin"

msgctxt "roofing_extruder_nr label"
msgid "Top Surface Skin Extruder"
msgstr "Extruder bovenskin"

msgctxt "roofing_material_flow label"
msgid "Top Surface Skin Flow"
msgstr "Bovenste oppervlak skindoorvoer"

msgctxt "jerk_roofing label"
msgid "Top Surface Skin Jerk"
msgstr "Schok bovenskin"

msgctxt "roofing_layer_count label"
msgid "Top Surface Skin Layers"
msgstr "Bovenste skinlagen"

msgctxt "roofing_angles label"
msgid "Top Surface Skin Line Directions"
msgstr "Lijnrichting bovenskin"

msgctxt "roofing_line_width label"
msgid "Top Surface Skin Line Width"
msgstr "Lijnbreedte bovenskin"

msgctxt "roofing_pattern label"
msgid "Top Surface Skin Pattern"
msgstr "Patroon bovenskin"

msgctxt "speed_roofing label"
msgid "Top Surface Skin Speed"
msgstr "Snelheid bovenskin"

msgctxt "top_thickness label"
msgid "Top Thickness"
msgstr "Dikte Bovenkant"

msgctxt "max_skin_angle_for_expansion description"
msgid "Top and/or bottom surfaces of your object with an angle larger than this setting, won't have their top/bottom skin expanded. This avoids expanding the narrow skin areas that are created when the model surface has a near vertical slope. An angle of 0° is horizontal and will cause no skin to be expanded, while an angle of 90° is vertical and will cause all skin to be expanded."
msgstr "Van boven- en/of onderoppervlakken van het object met een hoek die groter is dan deze instelling, wordt de boven-/onderskin niet uitgebreid. Hiermee wordt uitbreiding voorkomen van smalle skingebieden die worden gemaakt wanneer het modeloppervlak een nagenoeg verticale helling heeft. Bij een hoek van 0° (horizontaal) wordt er geen skin uitgebreid; bij een hoek van 90° (verticaal) wordt alle skin uitgebreid."

msgctxt "top_bottom description"
msgid "Top/Bottom"
msgstr "Boven-/onderkant"

msgctxt "top_bottom label"
msgid "Top/Bottom"
msgstr "Boven-/onderkant"

msgctxt "acceleration_topbottom label"
msgid "Top/Bottom Acceleration"
msgstr "Acceleratie Boven-/Onderkant"

msgctxt "top_bottom_extruder_nr label"
msgid "Top/Bottom Extruder"
msgstr "Extruder Boven-/Onderkant"

msgctxt "skin_material_flow label"
msgid "Top/Bottom Flow"
msgstr "Doorvoer boven/onder"

msgctxt "jerk_topbottom label"
msgid "Top/Bottom Jerk"
msgstr "Schok Boven-/Onderkant"

msgctxt "skin_angles label"
msgid "Top/Bottom Line Directions"
msgstr "Lijnrichtingen boven-/onderkant"

msgctxt "skin_line_width label"
msgid "Top/Bottom Line Width"
msgstr "Lijnbreedte Boven-/onderkant"

msgctxt "top_bottom_pattern label"
msgid "Top/Bottom Pattern"
msgstr "Patroon Boven-/Onderkant"

msgctxt "speed_topbottom label"
msgid "Top/Bottom Speed"
msgstr "Snelheid Boven-/Onderkant"

msgctxt "top_bottom_thickness label"
msgid "Top/Bottom Thickness"
msgstr "Dikte Boven-/Onderkant"

msgctxt "support_type option buildplate"
msgid "Touching Buildplate"
msgstr "Platform Aanraken"

msgctxt "support_tower_diameter label"
msgid "Tower Diameter"
msgstr "Pijlerdiameter"

msgctxt "support_tower_roof_angle label"
msgid "Tower Roof Angle"
msgstr "Hoek van Pijlerdak"

msgctxt "mesh_rotation_matrix description"
msgid "Transformation matrix to be applied to the model when loading it from file."
msgstr "Omzettingsmatrix die moet worden toegepast op het model wanneer dit wordt geladen vanuit een bestand."

msgctxt "travel label"
msgid "Travel"
msgstr "Beweging"

msgctxt "acceleration_travel label"
msgid "Travel Acceleration"
msgstr "Bewegingsacceleratie"

msgctxt "travel_avoid_distance label"
msgid "Travel Avoid Distance"
msgstr "Mijdafstand Tijdens Bewegingen"

msgctxt "jerk_travel label"
msgid "Travel Jerk"
msgstr "Bewegingsschok"

msgctxt "speed_travel label"
msgid "Travel Speed"
msgstr "Bewegingssnelheid"

msgctxt "magic_mesh_surface_mode description"
msgid "Treat the model as a surface only, a volume, or volumes with loose surfaces. The normal print mode only prints enclosed volumes. \"Surface\" prints a single wall tracing the mesh surface with no infill and no top/bottom skin. \"Both\" prints enclosed volumes like normal and any remaining polygons as surfaces."
msgstr "Behandel het model alleen als oppervlak, volume of volumen met losse oppervlakken. In de normale printmodus worden alleen omsloten volumen geprint. Met de optie 'Oppervlak' wordt een enkele wand geprint waarbij het rasteroppervlak wordt gevolgd zonder vulling en zonder boven-/onderskin. Met de optie 'Beide' worden omsloten volumen normaal geprint en eventuele resterende polygonen als oppervlakken."

msgctxt "support_structure option tree"
msgid "Tree"
msgstr "Boom"

msgctxt "infill_pattern option trihexagon"
msgid "Tri-Hexagon"
msgstr "Tri-hexagonaal"

msgctxt "infill_pattern option triangles"
msgid "Triangles"
msgstr "Driehoeken"

msgctxt "support_bottom_pattern option triangles"
msgid "Triangles"
msgstr "Driehoeken"

msgctxt "support_interface_pattern option triangles"
msgid "Triangles"
msgstr "Driehoeken"

msgctxt "support_pattern option triangles"
msgid "Triangles"
msgstr "Driehoeken"

msgctxt "support_roof_pattern option triangles"
msgid "Triangles"
msgstr "Driehoeken"

msgctxt "support_tree_max_diameter label"
msgid "Trunk Diameter"
msgstr "Stamdiameter"

msgctxt "machine_gcode_flavor option UltiGCode"
msgid "Ultimaker 2"
msgstr "Ultimaker 2"

msgctxt "meshfix_union_all label"
msgid "Union Overlapping Volumes"
msgstr "Overlappende Volumes Samenvoegen"

msgctxt "bridge_wall_min_length description"
msgid "Unsupported walls shorter than this will be printed using the normal wall settings. Longer unsupported walls will be printed using the bridge wall settings."
msgstr "Niet-ondersteunde wanden die korter zijn dan deze waarde, worden geprint met de normale wandinstellingen. Langere niet-ondersteunde wanden worden geprint met de instellingen voor brugwanden."

msgctxt "adaptive_layer_height_enabled label"
msgid "Use Adaptive Layers"
msgstr "Adaptieve lagen gebruiken"

msgctxt "support_use_towers label"
msgid "Use Towers"
msgstr "Pijlers Gebruiken"

msgctxt "acceleration_travel_enabled description"
msgid "Use a separate acceleration rate for travel moves. If disabled, travel moves will use the acceleration value of the printed line at their destination."
msgstr "Gebruik een aparte acceleratiesnelheid voor verplaatsingen. Indien uitgeschakeld, gebruikt de beweging de acceleratiewaarde van de geprinte lijn op de bestemming."

msgctxt "jerk_travel_enabled description"
msgid "Use a separate jerk rate for travel moves. If disabled, travel moves will use the jerk value of the printed line at their destination."
msgstr "Gebruik een apart schokpercentage voor verplaatsingen. Indien uitgeschakeld, gebruikt de beweging de schokwaarde van de geprinte lijn op de bestemming."

msgctxt "relative_extrusion description"
msgid "Use relative extrusion rather than absolute extrusion. Using relative E-steps makes for easier post-processing of the g-code. However, it's not supported by all printers and it may produce very slight deviations in the amount of deposited material compared to absolute E-steps. Irrespective of this setting, the extrusion mode will always be set to absolute before any g-code script is output."
msgstr "Gebruik relatieve extrusie in plaats van absolute extrusie. Bij het gebruik van relatieve E-steps wordt het nabewerken van G-code gemakkelijker. Deze optie wordt echter niet door alle printers ondersteund en kan lichte afwijkingen veroorzaken in de hoeveelheid afgezet materiaal ten opzichte van absolute E-steps. Ongeacht deze instelling wordt de extrusiemodus altijd ingesteld op absoluut voordat er een G-code-script wordt uitgevoerd."

msgctxt "support_use_towers description"
msgid "Use specialized towers to support tiny overhang areas. These towers have a larger diameter than the region they support. Near the overhang the towers' diameter decreases, forming a roof."
msgstr "Gebruik speciale pijlers om delen met minimale overhang te ondersteunen. Deze pijlers hebben een grotere diameter dan het deel dat ze ondersteunen. Bij de overhang neemt de diameter van de pijlers af en vormen ze een dak."

msgctxt "infill_mesh description"
msgid "Use this mesh to modify the infill of other meshes with which it overlaps. Replaces infill regions of other meshes with regions for this mesh. It's suggested to only print one Wall and no Top/Bottom Skin for this mesh."
msgstr "Gebruik dit raster om de vulling aan te passen van andere rasters waarmee dit raster overlapt. Met deze optie vervangt u vulgebieden van andere rasters met gebieden van dit raster. Het wordt aangeraden voor dit raster slechts één wand en geen boven-/onderskin te printen."

msgctxt "support_mesh description"
msgid "Use this mesh to specify support areas. This can be used to generate support structure."
msgstr "Gebruik dit raster om steunvlakken op te geven. Deze functie kan worden gebruikt om supportstructuur te genereren."

msgctxt "anti_overhang_mesh description"
msgid "Use this mesh to specify where no part of the model should be detected as overhang. This can be used to remove unwanted support structure."
msgstr "Gebruik dit raster om op te geven waar geen enkel deel van het model mag worden gedetecteerd als overhang. Deze functie kan worden gebruikt om ongewenste supportstructuur te verwijderen."

msgctxt "z_seam_type option back"
msgid "User Specified"
msgstr "Door de gebruiker opgegeven"

msgctxt "material_shrinkage_percentage_z label"
msgid "Vertical Scaling Factor Shrinkage Compensation"
msgstr "Verticale schaalfactor krimpcompensatie"

msgctxt "slicing_tolerance description"
msgid "Vertical tolerance in the sliced layers. The contours of a layer are normally generated by taking cross sections through the middle of each layer's thickness (Middle). Alternatively each layer can have the areas which fall inside of the volume throughout the entire thickness of the layer (Exclusive) or a layer has the areas which fall inside anywhere within the layer (Inclusive). Inclusive retains the most details, Exclusive makes for the best fit and Middle stays closest to the original surface."
msgstr "Verticale tolerantie in de gesneden lagen. De contouren van een laag kunnen worden normaal gesproken gegenereerd door dwarsdoorsneden te nemen door het midden van de dikte van de laag (Midden). Daarnaast kan elke laag gebieden hebben die over de gehele dikte van de laag binnen het volume vallen (Exclusief), of kan een laag gebieden hebben die overal binnen de laag vallen (Inclusief). Met Inclusief worden de meeste details behouden, met Exclusief verkrijgt u de beste pasvorm en met Midden behoudt u het originele oppervlak het meest."

msgctxt "material_bed_temp_wait label"
msgid "Wait for Build Plate Heatup"
msgstr "Wachten op verwarmen van platform"

msgctxt "material_print_temp_wait label"
msgid "Wait for Nozzle Heatup"
msgstr "Wachten op verwarmen van nozzle"

msgctxt "acceleration_wall label"
msgid "Wall Acceleration"
msgstr "Wandacceleratie"

msgctxt "wall_distribution_count label"
msgid "Wall Distribution Count"
msgstr "Aantal wanden voor distributie"

msgctxt "wall_extruder_nr label"
msgid "Wall Extruder"
msgstr "Wandextruder"

msgctxt "wall_material_flow label"
msgid "Wall Flow"
msgstr "Wanddoorvoer"

msgctxt "jerk_wall label"
msgid "Wall Jerk"
msgstr "Wandschok"

msgctxt "wall_line_count label"
msgid "Wall Line Count"
msgstr "Aantal Wandlijnen"

msgctxt "wall_line_width label"
msgid "Wall Line Width"
msgstr "Lijnbreedte Wand"

msgctxt "inset_direction label"
msgid "Wall Ordering"
msgstr "Wandvolgorde"

msgctxt "speed_wall label"
msgid "Wall Speed"
msgstr "Wandsnelheid"

msgctxt "wall_thickness label"
msgid "Wall Thickness"
msgstr "Wanddikte"

msgctxt "wall_transition_length label"
msgid "Wall Transition Length"
msgstr "Lengte wandovergang"

msgctxt "wall_transition_filter_distance label"
msgid "Wall Transitioning Filter Distance"
msgstr "Filterafstand wandovergang"

msgctxt "wall_transition_filter_deviation label"
msgid "Wall Transitioning Filter Margin"
msgstr "Filtermarge wandovergang"

msgctxt "wall_transition_angle label"
msgid "Wall Transitioning Threshold Angle"
msgstr "Drempelhoek wandovergang"

msgctxt "shell label"
msgid "Walls"
msgstr "Wanden"

msgctxt "wall_overhang_angle description"
msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either."
msgstr "Wanden die overhangen in een hoek groter dan deze waarde, worden geprint met instellingen voor overhangende wanden. Wanneer de waarde 90 is, wordt een wand niet als een overhangende wand gezien. Een overhang die wordt ondersteund door ondersteuning wordt ook niet als overhang gezien."

msgctxt "meshfix_fluid_motion_enabled description"
msgid "When enabled tool paths are corrected for printers with smooth motion planners. Small movements that deviate from the general tool path direction are smoothed to improve fluid motions."
msgstr "Indien ingeschakeld, worden gereedschapsbanen gecorrigeerd voor printers met vloeiende bewegingsplanners. Kleine bewegingen die afwijken van de algemene richting van het gereedschapspad worden afgevlakt om vloeiende bewegingen te verbeteren."

msgctxt "infill_enable_travel_optimization description"
msgid "When enabled, the order in which the infill lines are printed is optimized to reduce the distance travelled. The reduction in travel time achieved very much depends on the model being sliced, infill pattern, density, etc. Note that, for some models that have many small areas of infill, the time to slice the model may be greatly increased."
msgstr "Wanneer deze optie is ingeschakeld, wordt de volgorde geoptimaliseerd waarin de vullijnen worden geprint om de afgelegde beweging te reduceren. De reductie in bewegingstijd die wordt bereikt, is in hoge mate afhankelijk van het model dat wordt geslicet, het vulpatroon, de dichtheid enz. Houd er rekening mee dat de slicetijd voor modellen met veel kleine vulgebieden aanzienlijk kan worden verlengd."

msgctxt "support_fan_enable description"
msgid "When enabled, the print cooling fan speed is altered for the skin regions immediately above the support."
msgstr "Wanneer deze optie ingeschakeld is, wordt de ventilatorsnelheid voor het koelen van de print gewijzigd voor de skinregio's direct boven de supportstructuur."

msgctxt "z_seam_relative description"
msgid "When enabled, the z seam coordinates are relative to each part's centre. When disabled, the coordinates define an absolute position on the build plate."
msgstr "Als deze optie ingeschakeld is, zijn de Z-naadcoördinaten relatief ten opzichte van het midden van elk deel. Als de optie uitgeschakeld is, staan de coördinaten voor een absolute positie op het platform."

msgctxt "retraction_combing_max_distance description"
msgid "When greater than zero, combing travel moves that are longer than this distance will use retraction. If set to zero, there is no maximum and combing moves will not use retraction."
msgstr "Wanneer dit groter dan nul is, vindt bij een combing-beweging die langer is dan deze afstand, intrekking plaats. Wanneer dit nul is, is er geen maximum en vindt bij combing-bewegingen geen intrekking plaats."

msgctxt "hole_xy_offset_max_diameter description"
msgid "When greater than zero, the Hole Horizontal Expansion is gradually applied on small holes (small holes are expanded more). When set to zero the Hole Horizontal Expansion will be applied to all holes. Holes larger than the Hole Horizontal Expansion Max Diameter are not expanded."
msgstr "Als dit groter is dan nul, wordt de horizontale gatexpansie geleidelijk toegepast op kleine gaten (kleine gaten worden meer uitgebreid). Indien ingesteld op nul, wordt de horizontale gatexpansie toegepast op alle gaten. Gaten groter dan de maximale diameter van de horizontale gatexpansie worden niet vergroot."

msgctxt "hole_xy_offset description"
msgid "When greater than zero, the Hole Horizontal Expansion is the amount of offset applied to all holes in each layer. Positive values increase the size of the holes, negative values reduce the size of the holes. When this setting is enabled it can be further tuned with Hole Horizontal Expansion Max Diameter."
msgstr "Als de horizontale uitzetting van het gat groter is dan nul, is dit de hoeveelheid offset die op alle gaten in elke laag wordt toegepast. Positieve waarden vergroten de grootte van de gaten, negatieve waarden verkleinen de grootte van de gaten. Wanneer deze instelling is ingeschakeld, kan deze verder worden afgesteld met Maximum diameter horizontale uitzetting gaten."

msgctxt "bridge_skin_material_flow description"
msgid "When printing bridge skin regions, the amount of material extruded is multiplied by this value."
msgstr "Tijdens het printen van brugskinregio's wordt de hoeveelheid materiaal die wordt doorgevoerd, met deze waarde vermenigvuldigd."

msgctxt "bridge_wall_material_flow description"
msgid "When printing bridge walls, the amount of material extruded is multiplied by this value."
msgstr "Tijdens het printen van brugwanden wordt de hoeveelheid materiaal die wordt doorgevoerd, met deze waarde vermenigvuldigd."

msgctxt "bridge_skin_material_flow_2 description"
msgid "When printing the second bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "Tijdens het printen van de tweede brugskinlaag wordt de hoeveelheid materiaal die wordt doorgevoerd, met deze waarde vermenigvuldigd."

msgctxt "bridge_skin_material_flow_3 description"
msgid "When printing the third bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "Tijdens het printen van de derde brugskinlaag wordt de hoeveelheid materiaal die wordt doorgevoerd, met deze waarde vermenigvuldigd."

msgctxt "cool_lift_head description"
msgid "When the minimum speed is hit because of minimum layer time, lift the head away from the print and wait the extra time until the minimum layer time is reached."
msgstr "Wanneer de minimale snelheid wordt bereikt vanwege de minimale laagtijd, wordt de printkop van de print verwijderd totdat de minimale laagtijd bereikt is."

msgctxt "skin_no_small_gaps_heuristic description"
msgid "When the model has small vertical gaps of only a few layers, there should normally be skin around those layers in the narrow space. Enable this setting to not generate skin if the vertical gap is very small. This improves printing time and slicing time, but technically leaves infill exposed to the air."
msgstr "Als het model kleine verticale gaten van slechts een paar lagen heeft, bevindt er zich doorgaans een skin rond die lagen in de kleine ruimte. Schakel deze instelling in om geen skin te genereren als de verticale tussenruimte erg klein is. Zo verloopt printen en slicen sneller, maar technisch nadeel is dat de vulling aan de lucht wordt blootgesteld."

msgctxt "wall_transition_angle description"
msgid "When to create transitions between even and odd numbers of walls. A wedge shape with an angle greater than this setting will not have transitions and no walls will be printed in the center to fill the remaining space. Reducing this setting reduces the number and length of these center walls, but may leave gaps or overextrude."
msgstr "Wanneer u overgangen moet maken tussen even en oneven aantallen wanden. Een wigvorm met een hoek die groter is dan deze instelling, heeft geen overgangen. Er worden geen wanden geprint in het midden om de overblijvende ruimte te vullen. Door deze instelling kleiner te maken, reduceert u het aantal en de lengte van deze centrumwanden. Dit kan echter leiden tot openingen of een te hoge doorvoer."

msgctxt "wall_transition_length description"
msgid "When transitioning between different numbers of walls as the part becomes thinner, a certain amount of space is allotted to split or join the wall lines."
msgstr "Bij de overgang tussen verschillende aantallen wanden naarmate het onderdeel dunner wordt, wordt een bepaalde hoeveelheid ruimte toegewezen voor het splitsen of samenvoegen van wandlijnen."

msgctxt "wipe_hop_enable description"
msgid "When wiping, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "Tijdens het afvegen wordt het platform omlaag gebracht om ruimte te creëren tussen de nozzle en de print. Hiermee wordt voorkomen dat de nozzle de print raakt tijdens een beweging en wordt de kans verkleind dat de print van het platform wordt gestoten."

msgctxt "retraction_hop_enabled description"
msgid "Whenever a retraction is done, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "Tijdens het intrekken wordt het platform omlaag gebracht om ruimte te creëren tussen de nozzle en de print. Hiermee wordt voorkomen dat de nozzle de print raakt tijdens een beweging en wordt de kans verkleind dat de print van het platform wordt gestoten."

msgctxt "support_xy_overrides_z description"
msgid "Whether the Support X/Y Distance overrides the Support Z Distance or vice versa. When X/Y overrides Z the X/Y distance can push away the support from the model, influencing the actual Z distance to the overhang. We can disable this by not applying the X/Y distance around overhangs."
msgstr "Hiermee bepaalt u of de optie X-/Y-afstand supportstructuur voorrang krijgt boven de optie Z-afstand supportstructuur of vice versa. Wanneer X/Y voorrang krijgt boven Z, kan de X-/Y-afstand de supportstructuur wegduwen van het model, waardoor de daadwerkelijke Z-afstand tot de overhang wordt beïnvloed. Dit kan worden uitgeschakeld door de X-/Y-afstand niet toe te passen rond een overhang."

msgctxt "machine_center_is_zero description"
msgid "Whether the X/Y coordinates of the zero position of the printer is at the center of the printable area."
msgstr "Hiermee geeft u aan of de X/Y-coördinaten van de nul-positie van de printer zich in het midden van het printbare gebied bevinden."

msgctxt "machine_endstop_positive_direction_x description"
msgid "Whether the endstop of the X axis is in the positive direction (high X coordinate) or negative (low X coordinate)."
msgstr "Of de eindstop op de X-as zich in positieve (hoog X-coördinaat) of negatieve richting (laag X-coördinaat) bevindt."

msgctxt "machine_endstop_positive_direction_y description"
msgid "Whether the endstop of the Y axis is in the positive direction (high Y coordinate) or negative (low Y coordinate)."
msgstr "Of de eindstop op de Y-as zich in positieve (hoog Y-coördinaat) of negatieve richting (laag Y-coördinaat) bevindt."

msgctxt "machine_endstop_positive_direction_z description"
msgid "Whether the endstop of the Z axis is in the positive direction (high Z coordinate) or negative (low Z coordinate)."
msgstr "Of de eindstop op de Z-as zich in positieve (hoog Z-coördinaat) of negatieve richting (laag Z-coördinaat) bevindt."

msgctxt "machine_extruders_share_heater description"
msgid "Whether the extruders share a single heater rather than each extruder having its own heater."
msgstr "Hiermee bepaalt u of de extruders één verwarming delen in plaats van dat elke extruder zijn eigen verwarming heeft."

msgctxt "machine_extruders_share_nozzle description"
msgid "Whether the extruders share a single nozzle rather than each extruder having its own nozzle. When set to true, it is expected that the printer-start gcode script properly sets up all extruders in an initial retraction state that is known and mutually compatible (either zero or one filament not retracted); in that case the initial retraction status is described, per extruder, by the 'machine_extruders_shared_nozzle_initial_retraction' parameter."
msgstr "Hiermee bepaalt u of de extruders één nozzle delen in plaats van dat elke extruder zijn eigen nozzle heeft. Wanneer dit wordt ingesteld op 'true', wordt verwacht dat het G-code-script voor het opstarten van de printer alle extruders correct instelt in een initiële intrekstatus die bekend is en onderling compatibel is (nul of één filament niet ingetrokken). In dat geval wordt de initiële intrekstatus per extruder beschreven door de parameter 'machine_extruders_shared_nozzle_initial_retraction'."

msgctxt "machine_heated_bed description"
msgid "Whether the machine has a heated build plate present."
msgstr "Hiermee geeft u aan of een verwarmd platform aanwezig is."

msgctxt "machine_heated_build_volume description"
msgid "Whether the machine is able to stabilize the build volume temperature."
msgstr "Of de machine in staat is de temperatuur van het werkvolume te stabiliseren."

msgctxt "center_object description"
msgid "Whether to center the object on the middle of the build platform (0,0), instead of using the coordinate system in which the object was saved."
msgstr "Hiermee bepaalt u of het object in het midden van het platform moet worden gecentreerd (0,0) of dat het coördinatensysteem moet worden gebruikt waarin het object opgeslagen is."

msgctxt "machine_nozzle_temp_enabled description"
msgid "Whether to control temperature from Cura. Turn this off to control nozzle temperature from outside of Cura."
msgstr "Hiermee geeft u aan of u de temperatuur wilt reguleren vanuit Cura. Schakel deze optie uit als u de nozzletemperatuur buiten Cura om wilt reguleren."

msgctxt "material_bed_temp_prepend description"
msgid "Whether to include build plate temperature commands at the start of the gcode. When the start_gcode already contains build plate temperature commands Cura frontend will automatically disable this setting."
msgstr "Hiermee bepaalt u of aan het begin van de G-code opdrachten voor de platformtemperatuur moeten worden ingevoegd. Wanneer de start-g-code al opdrachten voor de platformtemperatuur bevat, wordt deze instelling automatisch uitgeschakeld door de Cura-frontend."

msgctxt "material_print_temp_prepend description"
msgid "Whether to include nozzle temperature commands at the start of the gcode. When the start_gcode already contains nozzle temperature commands Cura frontend will automatically disable this setting."
msgstr "Hiermee bepaalt u of aan het begin van de G-code opdrachten voor de nozzletemperatuur moeten worden ingevoegd. Wanneer de start-g-code al opdrachten voor de nozzletemperatuur bevat, wordt deze instelling automatisch uitgeschakeld door de Cura-frontend."

msgctxt "clean_between_layers description"
msgid "Whether to include nozzle wipe G-Code between layers (maximum 1 per layer). Enabling this setting could influence behavior of retract at layer change. Please use Wipe Retraction settings to control retraction at layers where the wipe script will be working."
msgstr "Hiermee bepaalt u of u het afvegen van de nozzle tussen lagen wilt opnemen in de G-code. Het inschakelen van deze optie kan het gedrag van het intrekken bij de laagwissel beïnvloeden. Gebruik de instellingen voor Intrekken voor afvegen om het intrekken te regelen bij lagen waarbij het afveegscript actief is."

msgctxt "material_bed_temp_wait description"
msgid "Whether to insert a command to wait until the build plate temperature is reached at the start."
msgstr "Hiermee bepaalt u of de opdracht moet worden ingevoegd dat bij aanvang moet worden gewacht totdat het platform op temperatuur is."

msgctxt "prime_blob_enable description"
msgid "Whether to prime the filament with a blob before printing. Turning this setting on will ensure that the extruder will have material ready at the nozzle before printing. Printing Brim or Skirt can act like priming too, in which case turning this setting off saves some time."
msgstr "Hiermee bepaalt u of het filament voor het printen met een blob wordt geprimed. Met het inschakelen van deze instelling wordt verzekerd dat er vanuit de extruder materiaal bij de nozzle beschikbaar is voordat het printen start. Het printen van een brim of skirt kan tevens fungeren als primen. In dat geval kan door het uitschakelen van deze instelling tijd worden bespaard."

msgctxt "print_sequence description"
msgid "Whether to print all models one layer at a time or to wait for one model to finish, before moving on to the next. One at a time mode is possible if a) only one extruder is enabled and b) all models are separated in such a way that the whole print head can move in between and all models are lower than the distance between the nozzle and the X/Y axes."
msgstr "Hiermee bepaalt u of alle modellen laag voor laag moeten worden geprint of dat eerst het ene model helemaal klaar moet zijn voordat aan het volgende wordt begonnen. Eén voor één printen is mogelijk als a) slechts één extruder is ingeschakeld en b) alle modellen zodanig zijn gescheiden dat de hele printkop ertussen kan bewegen en alle modellen lager zijn dan de afstand tussen de nozzle en de X/Y-assen."

msgctxt "machine_show_variants description"
msgid "Whether to show the different variants of this machine, which are described in separate json files."
msgstr "Hiermee bepaalt u of verschillende varianten van deze machine worden getoond. Deze worden beschreven in afzonderlijke json-bestanden."

msgctxt "machine_firmware_retract description"
msgid "Whether to use firmware retract commands (G10/G11) instead of using the E property in G1 commands to retract the material."
msgstr "Hiermee bepaalt u of u voor het intrekken van materiaal firmwareopdrachten voor intrekken (G10/G11) gebruikt in plaats van de eigenschap E in G1-opdrachten."

msgctxt "material_print_temp_wait description"
msgid "Whether to wait until the nozzle temperature is reached at the start."
msgstr "Hiermee bepaalt u of bij aanvang moet worden gewacht totdat de nozzle op temperatuur is."

msgctxt "infill_line_width description"
msgid "Width of a single infill line."
msgstr "Breedte van een enkele vullijn."

msgctxt "support_interface_line_width description"
msgid "Width of a single line of support roof or floor."
msgstr "Breedte van een enkele lijn van het supportdak of de supportvloer."

msgctxt "roofing_line_width description"
msgid "Width of a single line of the areas at the top of the print."
msgstr "Breedte van een enkele lijn aan de bovenkant van de print."

msgctxt "line_width description"
msgid "Width of a single line. Generally, the width of each line should correspond to the width of the nozzle. However, slightly reducing this value could produce better prints."
msgstr "De breedte van een enkele lijn. Over het algemeen dient de breedte van elke lijn overeen te komen met de breedte van de nozzle. Wanneer deze waarde echter iets wordt verlaagd, resulteert dit in betere prints."

msgctxt "prime_tower_line_width description"
msgid "Width of a single prime tower line."
msgstr "Breedte van een enkele lijn van de primepijler."

msgctxt "skirt_brim_line_width description"
msgid "Width of a single skirt or brim line."
msgstr "Breedte van een enkele skirt- of brimlijn."

msgctxt "support_bottom_line_width description"
msgid "Width of a single support floor line."
msgstr "Breedte van een enkele lijn van de supportvloer."

msgctxt "support_roof_line_width description"
msgid "Width of a single support roof line."
msgstr "Breedte van een enkele lijn van het supportdak."

msgctxt "support_line_width description"
msgid "Width of a single support structure line."
msgstr "Breedte van een enkele lijn van de supportstructuur."

msgctxt "skin_line_width description"
msgid "Width of a single top/bottom line."
msgstr "Breedte van een enkele lijn aan de boven-/onderkant."

msgctxt "wall_line_width_x description"
msgid "Width of a single wall line for all wall lines except the outermost one."
msgstr "Breedte van een enkele wandlijn voor alle wandlijnen, behalve de buitenste."

msgctxt "wall_line_width description"
msgid "Width of a single wall line."
msgstr "Breedte van een enkele wandlijn."

msgctxt "raft_base_line_width description"
msgid "Width of the lines in the base raft layer. These should be thick lines to assist in build plate adhesion."
msgstr "Breedte van de lijnen van de onderste laag van de raft. Deze lijnen moeten dik zijn om een betere hechting aan het platform mogelijk te maken."

msgctxt "raft_interface_line_width description"
msgid "Width of the lines in the middle raft layer. Making the second layer extrude more causes the lines to stick to the build plate."
msgstr "Breedte van de lijnen in de middelste laag van de raft. Als u voor de tweede laag meer materiaal gebruikt, hechten de lijnen beter aan het platform."

msgctxt "raft_surface_line_width description"
msgid "Width of the lines in the top surface of the raft. These can be thin lines so that the top of the raft becomes smooth."
msgstr "De breedte van de lijnen in de bovenkant van de raft. Dit kunnen dunne lijnen zijn, zodat de bovenkant van de raft glad wordt."

msgctxt "wall_line_width_0 description"
msgid "Width of the outermost wall line. By lowering this value, higher levels of detail can be printed."
msgstr "De breedte van de buitenste lijn van de wand. Wanneer deze waarde wordt verlaagd, kan nauwkeuriger worden geprint."

msgctxt "min_bead_width description"
msgid "Width of the wall that will replace thin features (according to the Minimum Feature Size) of the model. If the Minimum Wall Line Width is thinner than the thickness of the feature, the wall will become as thick as the feature itself."
msgstr "Breedte van de wand die dunne elementen van het model vervangt (volgens de minimum elementgrootte). Als de Minimumbreedte wandlijn dunner is dan de dikte van het element, wordt de wand even dik als het element zelf."

msgctxt "wipe_brush_pos_x label"
msgid "Wipe Brush X Position"
msgstr "X-positie afveegborstel"

msgctxt "wipe_hop_speed label"
msgid "Wipe Hop Speed"
msgstr "Sprongsnelheid voor afvegen"

msgctxt "prime_tower_wipe_enabled label"
msgid "Wipe Inactive Nozzle on Prime Tower"
msgstr "Inactieve nozzle vegen op primepijler"

msgctxt "wipe_move_distance label"
msgid "Wipe Move Distance"
msgstr "Verplaatsingsafstand voor afvegen"

msgctxt "clean_between_layers label"
msgid "Wipe Nozzle Between Layers"
msgstr "Nozzle afvegen tussen lagen"

msgctxt "wipe_pause label"
msgid "Wipe Pause"
msgstr "Afvegen pauzeren"

msgctxt "wipe_repeat_count label"
msgid "Wipe Repeat Count"
msgstr "Aantal afveegbewegingen"

msgctxt "wipe_retraction_amount label"
msgid "Wipe Retraction Distance"
msgstr "Intrekafstand voor afvegen"

msgctxt "wipe_retraction_enable label"
msgid "Wipe Retraction Enable"
msgstr "Intrekken voor afvegen inschakelen"

msgctxt "wipe_retraction_extra_prime_amount label"
msgid "Wipe Retraction Extra Prime Amount"
msgstr "Extra primehoeveelheid na intrekken voor afvegen"

msgctxt "wipe_retraction_prime_speed label"
msgid "Wipe Retraction Prime Speed"
msgstr "Primesnelheid Intrekken voor afvegen"

msgctxt "wipe_retraction_retract_speed label"
msgid "Wipe Retraction Retract Speed"
msgstr "Intreksnelheid voor afvegen (intrekken)"

msgctxt "wipe_retraction_speed label"
msgid "Wipe Retraction Speed"
msgstr "Intreksnelheid voor afvegen"

msgctxt "wipe_hop_enable label"
msgid "Wipe Z Hop"
msgstr "Z-sprong afvegen"

msgctxt "wipe_hop_amount label"
msgid "Wipe Z Hop Height"
msgstr "Hoogte Z-sprong voor afvegen"

msgctxt "retraction_combing option infill"
msgid "Within Infill"
msgstr "Binnen Vulling"

msgctxt "machine_always_write_active_tool description"
msgid "Write active tool after sending temp commands to inactive tool. Required for Dual Extruder printing with Smoothie or other firmware with modal tool commands."
msgstr "Tool voor actief schrijven na het verzenden van tijdelijke opdrachten naar inactieve tool. Vereist voor afdrukken met dubbele extruder met Smoothie of andere firmware met modale toolopdrachten."

msgctxt "machine_endstop_positive_direction_x label"
msgid "X Endstop in Positive Direction"
msgstr "X-eindstop in positieve richting"

msgctxt "wipe_brush_pos_x description"
msgid "X location where wipe script will start."
msgstr "X-positie waar afveegscript start."

msgctxt "support_xy_overrides_z option xy_overrides_z"
msgid "X/Y overrides Z"
msgstr "X/Y krijgt voorrang boven Z"

msgctxt "machine_endstop_positive_direction_y label"
msgid "Y Endstop in Positive Direction"
msgstr "Y-eindstop in positieve richting"

msgctxt "machine_endstop_positive_direction_z label"
msgid "Z Endstop in Positive Direction"
msgstr "Z-eindstop in positieve richting"

msgctxt "retraction_hop_after_extruder_switch label"
msgid "Z Hop After Extruder Switch"
msgstr "Z-sprong na Wisselen Extruder"

msgctxt "retraction_hop_after_extruder_switch_height label"
msgid "Z Hop After Extruder Switch Height"
msgstr "Hoogte Z-sprong na wisselen extruder"

msgctxt "retraction_hop label"
msgid "Z Hop Height"
msgstr "Hoogte Z-sprong"

msgctxt "retraction_hop_only_when_collides label"
msgid "Z Hop Only Over Printed Parts"
msgstr "Z-sprong Alleen over Geprinte Delen"

msgctxt "speed_z_hop label"
msgid "Z Hop Speed"
msgstr "Snelheid Z-sprong"

msgctxt "retraction_hop_enabled label"
msgid "Z Hop When Retracted"
msgstr "Z-sprong wanneer ingetrokken"

msgctxt "z_seam_type label"
msgid "Z Seam Alignment"
msgstr "Uitlijning Z-naad"

msgctxt "z_seam_position label"
msgid "Z Seam Position"
msgstr "Z-naadpositie"

msgctxt "z_seam_relative label"
msgid "Z Seam Relative"
msgstr "Relatieve Z-naad"

msgctxt "z_seam_x label"
msgid "Z Seam X"
msgstr "Z-naad X"

msgctxt "z_seam_y label"
msgid "Z Seam Y"
msgstr "Z-naad Y"

msgctxt "support_xy_overrides_z option z_overrides_xy"
msgid "Z overrides X/Y"
msgstr "Z krijgt voorrang boven X/Y"

msgctxt "infill_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"

msgctxt "ironing_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"

msgctxt "roofing_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"

msgctxt "support_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"

msgctxt "support_interface_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"

msgctxt "support_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"

msgctxt "support_roof_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"

msgctxt "top_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"

msgctxt "top_bottom_pattern_0 option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"

msgctxt "travel description"
msgid "travel"
msgstr "beweging"

msgctxt "cool_during_extruder_switch description"
msgid "<html>Whether to activate the cooling fans during a nozzle switch. This can help reducing oozing by cooling the nozzle faster:<ul><li><b>Unchanged:</b> keep the fans as they were previously</li><li><b>Only last extruder:</b> turn on the fan of the last used extruder, but turn the others off (if any). This is useful if you have completely separate extruders.</li><li><b>All fans:</b> turn on all fans during nozzle switch. This is useful if you have a single cooling fan, or multiple fans that stay close to each other.</li></ul></html>"
msgstr "<html>Of de koelventilatoren moeten worden geactiveerd bij een spuitkopwissel. Dit kan helpen om doorsijpelen te verminderen omdat de sproeier sneller afkoelt: <ul><li><b>Ongewijzigd:</b> houd de ventilatoren zoals ze waren</li><li><b>Alleen laatste extruder:</b>schakel de ventilator van de laatstgebruikte extruder in, maar schakel de andere uit (als die er zijn). Dit is nuttig indien u volledig aparte extruders heeft.</li><li><b>Alle ventilatoren:</b> schakel alle ventilatoren in bij een spuitkopwissel. Dit is nuttig indien u een enkele koelventilator heeft, of meerdere ventilatoren die dicht bij elkaar zitten.</li></ul></html>"

msgctxt "cool_during_extruder_switch option all_fans"
msgid "All fans"
msgstr "Alle ventilatoren"

msgctxt "cool_during_extruder_switch label"
msgid "Cooling during extruder switch"
msgstr "Koeling tijdens extruderwissel"

msgctxt "support_z_seam_away_from_model description"
msgid "Manage the spatial relationship between the z seam of the support structure and the actual 3D model. This control is crucial as it allows users to ensure the seamless removal of support structures post-printing, without inflicting damage or leaving marks on the printed model."
msgstr "Beheer de ruimtelijke relatie tussen de z-naad van de ondersteunende structuur en het eigenlijke 3D-model. Controle hierover is cruciaal omdat het gebruikers in staat stelt om de ondersteunende structuren na het printen naadloos te verwijderen, zonder schade of sporen op het geprinte model."

msgctxt "support_z_seam_min_distance label"
msgid "Min Z Seam Distance from Model"
msgstr "Min. Z-naadafstand van model"

msgctxt "support_infill_density_multiplier_initial_layer description"
msgid "Multiplier for the infill on the initial layers of the support. Increasing this may help for bed adhesion."
msgstr "Vermenigvuldiging voor de infill op de eerste lagen van de drager. Dit verhogen kan helpen voor de bedhechting."

msgctxt "cool_during_extruder_switch option only_last_extruder"
msgid "Only last extruder"
msgstr "Alleen laatste extruder"

msgctxt "z_seam_on_vertex description"
msgid "Place the z-seam on a polygon vertex. Switching this off can place the seam between vertices as well. (Keep in mind that this won't override the restrictions on placing the seam on an unsupported overhang.)"
msgstr "Plaats de z-naad op een polygoonvertex. Door dit uit te schakelen kan de naad ook tussen vertexen geplaatst worden. (Denk eraan dat dit niet de beperkingen opheft om de naad op een niet-ondersteunde overhang te plaatsen)."

msgctxt "prime_tower_min_shell_thickness label"
msgid "Prime Tower Minimum Shell Thickness"
msgstr "Minimale wanddikte primaire toren"

msgctxt "raft_base_flow label"
msgid "Raft Base Flow"
msgstr "Flow"

msgctxt "raft_base_infill_overlap_mm label"
msgid "Raft Base Infill Overlap"
msgstr "Infilloverlap raftbasis"

msgctxt "raft_base_infill_overlap label"
msgid "Raft Base Infill Overlap Percentage"
msgstr "Infilloverlappercentage raftbasis"

msgctxt "raft_flow label"
msgid "Raft Flow"
msgstr "Flow raft"

msgctxt "raft_interface_flow label"
msgid "Raft Interface Flow"
msgstr "Flow raftinterface"

msgctxt "raft_interface_infill_overlap_mm label"
msgid "Raft Interface Infill Overlap"
msgstr "Infilloverlap raftinterface"

msgctxt "raft_interface_infill_overlap label"
msgid "Raft Interface Infill Overlap Percentage"
msgstr "Infilloverlappercentage raftinterface"

msgctxt "raft_interface_z_offset label"
msgid "Raft Interface Z Offset"
msgstr "Z-offset raftinterface"

msgctxt "raft_surface_flow label"
msgid "Raft Surface Flow"
msgstr "Flow raftoppervlak"

msgctxt "raft_surface_infill_overlap_mm label"
msgid "Raft Surface Infill Overlap"
msgstr "Infilloverlap raftoppervlak"

msgctxt "raft_surface_infill_overlap label"
msgid "Raft Surface Infill Overlap Percentage"
msgstr "Infilloverlappercentage raftoppervlak"

msgctxt "raft_surface_z_offset label"
msgid "Raft Surface Z Offset"
msgstr "Z-offset raftoppervlak"

msgctxt "seam_overhang_angle label"
msgid "Seam Overhanging Wall Angle"
msgstr "Naad boven wandhoek"

msgctxt "support_infill_density_multiplier_initial_layer label"
msgid "Support Infill Density Multiplier Initial Layer"
msgstr "Ondersteuning vermenigvuldigen infilldichtheid eerste laag"

msgctxt "support_z_seam_away_from_model label"
msgid "Support Z Seam Away from Model"
msgstr "Ondersteuning Z-naad weg van model"

msgctxt "raft_base_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft base printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "De hoeveelheid materiaal, ten opzichte van een normale extrusielijn, die uitsteekt bij het printen van de raftbasis. Een verhoogde flow kan de hechting en de structurele sterkte van de raft verbeteren."

msgctxt "raft_interface_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft interface printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "De hoeveelheid materiaal, ten opzichte van een normale extrusielijn, die uitsteekt bij het printen van de raftinterface. Een verhoogde flow kan de hechting en de structurele sterkte van het vlot verbeteren."

msgctxt "raft_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "De hoeveelheid materiaal, ten opzichte van een normale extrusielijn, die uitsteekt bij het printen van de raft. Een verhoogde flow kan de hechting en de structurele sterkte van het vlot verbeteren."

msgctxt "raft_surface_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft surface printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "De hoeveelheid materiaal, ten opzichte van een normale extrusielijn, die uitsteekt bij het bedrukken van het raftoppervlak. Een verhoogde flow kan de hechting en de structurele sterkte van de raft verbeteren."

msgctxt "raft_base_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft base, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "De hoeveelheid overlap tussen de infill en de wanden van de raftbasis, als percentage van de breedte van de infill-lijn. Een kleine overlap zorgt ervoor dat de wanden stevig op de infill aansluiten."

msgctxt "raft_base_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft base. A slight overlap allows the walls to connect firmly to the infill."
msgstr "De hoeveelheid overlap tussen de infill en de wanden van de raftbasis. Een kleine overlap zorgt ervoor dat de wanden stevig op de infill aansluiten."

msgctxt "raft_interface_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft interface, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "De hoeveelheid overlap tussen de infill en de wanden van de raftinterface, als percentage van de breedte van de infill-lijn. Een kleine overlap zorgt ervoor dat de wanden stevig op de infill aansluiten."

msgctxt "raft_interface_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft interface. A slight overlap allows the walls to connect firmly to the infill."
msgstr "De hoeveelheid overlap tussen de infill en de wanden van de raftinterface. Een kleine overlap zorgt ervoor dat de wanden stevig op de infill aansluiten."

msgctxt "raft_surface_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft surface, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "De hoeveelheid overlap tussen de infill en de wanden van het raftoppervlak, als percentage van de breedte van de infill-lijn. Een kleine overlap zorgt ervoor dat de wanden stevig op de infill aansluiten."

msgctxt "raft_surface_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft surface. A slight overlap allows the walls to connect firmly to the infill."
msgstr "De hoeveelheid overlap tussen de infill en de wanden van het raftoppervlak. Een kleine overlap zorgt ervoor dat de wanden stevig op de infill aansluiten."

msgctxt "support_z_seam_min_distance description"
msgid "The distance between the model and its support structure at the z-axis seam."
msgstr "De afstand tussen het model en de ondersteuningsstructuur op de naad van de z-as."

msgctxt "prime_tower_min_shell_thickness description"
msgid "The minimum thickness of the prime tower shell. You may increase it to make the prime tower stronger."
msgstr "De minimale dikte van de wand van de primaire toren. U kunt deze dikte verhogen om de primaire toren sterker te maken."

msgctxt "seam_overhang_angle description"
msgid "Try to prevent seams on walls that overhang more than this angle. When the value is 90, no walls will be treated as overhanging."
msgstr "Probeer naden te voorkomen bij muren die verder overhangen dan deze hoek. Als de waarde 90 is, worden geen muren als overhangend behandeld."

msgctxt "cool_during_extruder_switch option unchanged"
msgid "Unchanged"
msgstr "Ongewijzigd"

msgctxt "raft_interface_z_offset description"
msgid "When printing the first layer of the raft interface, translate by this offset to customize the adhesion between base and interface. A negative offset should improve the adhesion."
msgstr "Bij het printen van de eerste laag van de raftinterface: gebruik deze offset om de hechting tussen de basis en de interface aan te passen. Een negatieve offset zou de hechting moeten verbeteren."

msgctxt "raft_surface_z_offset description"
msgid "When printing the first layer of the raft surface, translate by this offset to customize the adhesion between interface and surface. A negative offset should improve the adhesion."
msgstr "Bij het printen van de eerste laag van het raftoppervlak: gebruik deze offset om de hechting tussen de interface en het oppervlak aan te passen. Een negatieve offset zou de hechting moeten verbeteren."

msgctxt "z_seam_on_vertex label"
msgid "Z Seam On Vertex"
msgstr "Z-naad op vertex"

msgctxt "extra_infill_lines_to_support_skins description"
msgid "Add extra lines into the infill pattern to support skins above.   This option prevents holes or plastic blobs that sometime show in complex shaped skins due to the infill below not correctly supporting the skin layer being printed above.  'Walls' supports just the outlines of the skin, whereas 'Walls and Lines' also supports the ends of the lines that make up the skin."
msgstr "Voeg extra lijnen toe aan het invulpatroon om de skins erboven te ondersteunen. Deze optie voorkomt gaten of plastic klodders die soms te zien zijn in complex gevormde skins doordat de invulling eronder de skinlaag die erboven wordt geprint niet correct ondersteunt. 'Wanden' ondersteunt alleen de contouren van de skin, terwijl 'Wanden en lijnen' ook de uiteinden van de lijnen ondersteunt die de skin vormen."

msgctxt "build_fan_full_at_height label"
msgid "Build Fan Speed at Height"
msgstr "Constructieventilatorsnelheid op hoogte"

msgctxt "build_fan_full_layer label"
msgid "Build Fan Speed at Layer"
msgstr "Constructieventilatorsnelheid op laag"

msgctxt "build_volume_fan_nr label"
msgid "Build volume fan number"
msgstr "Ventilatornummer constructievolume "

msgctxt "scarf_split_distance description"
msgid "Determines the length of each step in the flow change when extruding along the scarf seam. A smaller distance will result in a more precise but also more complex G-code."
msgstr "Bepaalt de lengte van elke stap in de stroomverandering bij het extruderen langs de schuine naad. Een kleinere afstand resulteert in een nauwkeurigere maar ook complexere G-code."

msgctxt "scarf_joint_seam_length description"
msgid "Determines the length of the scarf seam, a seam type that should make the Z seam less visible. Must be higher than 0 to be effective."
msgstr "Bepaalt de lengte van de schuine naad, een naadtype dat de Z-naad minder zichtbaar moet maken. Moet hoger zijn dan 0 om effectief te zijn."

msgctxt "gradual_flow_discretisation_step_size description"
msgid "Duration of each step in the gradual flow change"
msgstr "Duur van elke stap in de geleidelijke stroomverandering"

msgctxt "gradual_flow_enabled description"
msgid "Enable gradual flow changes. When enabled, the flow is gradually increased/decreased to the target flow. This is useful for printers with a bowden tube where the flow is not immediately changed when the extruder motor starts/stops."
msgstr "Geleidelijke stroomwijzigingen inschakelen. Als deze optie is ingeschakeld, wordt de stroom geleidelijk verhoogd/verlaagd tot de doelstroom. Dit is handig voor printers met een bowdenbuis waarbij de stroom niet onmiddellijk verandert wanneer de extrudermotor start/stopt."

msgctxt "extra_infill_lines_to_support_skins label"
msgid "Extra Infill Lines To Support Skins"
msgstr "Extra invullijnen ter ondersteuning van skins"

msgctxt "reset_flow_duration description"
msgid "For any travel move longer than this value, the material flow is reset to the paths target flow"
msgstr "Voor elke af te leggen afstand die langer is dan deze waarde, wordt de materiaalstroom opnieuw ingesteld op de doelstroom van de paden."

msgctxt "gradual_flow_discretisation_step_size label"
msgid "Gradual flow discretisation step size"
msgstr "Stapgrootte geleidelijke stroomdiscretisatie"

msgctxt "gradual_flow_enabled label"
msgid "Gradual flow enabled"
msgstr "Geleidelijke stroom ingeschakeld"

msgctxt "max_flow_acceleration label"
msgid "Gradual flow max acceleration"
msgstr "Maximale versnelling voor geleidelijke stroom"

msgctxt "layer_0_max_flow_acceleration label"
msgid "Initial layer max flow acceleration"
msgstr "Maximale stroomversnelling eerste laag"

msgctxt "max_flow_acceleration description"
msgid "Maximum acceleration for gradual flow changes"
msgstr "Maximale versnelling voor geleidelijke stroomveranderingen"

msgctxt "layer_0_max_flow_acceleration description"
msgid "Minimum speed for gradual flow changes for the first layer"
msgstr "Minimumsnelheid voor geleidelijke stroomveranderingen voor de eerste laag"

msgctxt "extra_infill_lines_to_support_skins option none"
msgid "None"
msgstr "Geen"

msgctxt "wall_0_acceleration label"
msgid "Outer Wall Acceleration"
msgstr "Buitenwandversnelling"

msgctxt "wall_0_deceleration label"
msgid "Outer Wall Deceleration"
msgstr "Buitenwandvertraging"

msgctxt "wall_0_end_speed_ratio label"
msgid "Outer Wall End Speed Ratio"
msgstr "Snelheidsverhouding buitenwand"

msgctxt "wall_0_speed_split_distance label"
msgid "Outer Wall Speed Split Distance"
msgstr "Snelheid splitafstand buitenwand"

msgctxt "wall_0_start_speed_ratio label"
msgid "Outer Wall Start Speed Ratio"
msgstr "Startsnelheidsverhouding buitenwand"

msgctxt "reset_flow_duration label"
msgid "Reset flow duration"
msgstr "Stroomduur opnieuw instellen"

msgctxt "scarf_joint_seam_length label"
msgid "Scarf Seam Length"
msgstr "Lengte schuine naad"

msgctxt "scarf_joint_seam_start_height_ratio label"
msgid "Scarf Seam Start Height"
msgstr "Starthoogte schuine naad"

msgctxt "scarf_split_distance label"
msgid "Scarf Seam Step Length"
msgstr "Staplengte schuine naad"

msgctxt "build_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr "De hoogte waarop de ventilatoren op normale ventilatorsnelheid draaien. Op de lagen hieronder neemt de ventilatorsnelheid geleidelijk toe van de initiële ventilatorsnelheid naar de normale ventilatorsnelheid."

msgctxt "build_fan_full_layer description"
msgid "The layer at which the build fans spin on full fan speed. This value is calculated and rounded to a whole number."
msgstr "De laag waarbij de bouwventilatoren op volle ventilatorsnelheid draaien. Deze waarde wordt berekend en afgerond op een heel getal."

msgctxt "build_volume_fan_nr description"
msgid "The number of the fan that cools the build volume. If this is set to 0, it's means that there is no build volume fan"
msgstr "Het nummer van de ventilator die het constructievolume koelt. Als dit is ingesteld op 0, betekent dit dat er geen constructievolumeventilator is."

msgctxt "scarf_joint_seam_start_height_ratio description"
msgid "The ratio of the selected layer height at which the scarf seam will begin. A lower number will result in a larger seam height. Must be lower than 100 to be effective."
msgstr "De verhouding van de geselecteerde laaghoogte waarop de schuine naad zal beginnen. Een lager getal resulteert in een grotere naadhoogte. Moet lager zijn dan 100 om effectief te zijn."

msgctxt "wall_0_acceleration description"
msgid "This is the acceleration with which to reach the top speed when printing an outer wall."
msgstr "Dit is de versnelling waarmee men de topsnelheid bereikt als men een buitenwand afdrukt."

msgctxt "wall_0_deceleration description"
msgid "This is the deceleration with which to end printing an outer wall."
msgstr "Dit is de vertraging waarmee men het afdrukken van een buitenwand beëindigt."

msgctxt "wall_0_speed_split_distance description"
msgid "This is the maximum length of an extrusion path when splitting a longer path to apply the outer wall acceleration/deceleration. A smaller distance will create a more precise but also more verbose G-Code."
msgstr "Dit is de maximale lengte van een extrusiepad bij het splitsen van een langer pad om de versnelling/vertraging van de buitenwand toe te passen. Een kleinere afstand zorgt voor een preciezere maar ook uitgebreide G-code."

msgctxt "wall_0_end_speed_ratio description"
msgid "This is the ratio of the top speed to end with when printing an outer wall."
msgstr "Dit is de verhouding van de topsnelheid om mee te eindigen bij het printen van een buitenwand."

msgctxt "wall_0_start_speed_ratio description"
msgid "This is the ratio of the top speed to start with when printing an outer wall."
msgstr "Dit is de verhouding van de topsnelheid om mee te beginnen bij het printen van een buitenwand."

msgctxt "extra_infill_lines_to_support_skins option walls"
msgid "Walls Only"
msgstr "Alleen wanden"

msgctxt "extra_infill_lines_to_support_skins option walls_and_lines"
msgid "Walls and Lines"
msgstr "Wanden en lijnen"

msgctxt "wall_overhang_angle description"
msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either. Furthermore, any line that's less than half overhanging will also not be treated as overhang."
msgstr "Wanden die meer oversteken dan deze hoek worden afgedrukt met behulp van de instellingen voor overhangende wanden. Als de waarde 90 is, worden er geen wanden behandeld als overhangend. Overhangen die ondersteund worden door ondersteuning worden ook niet behandeld als overhang. Daarnaast wordt elke lijn die voor minder dan de helft overhangt ook niet behandeld als overhang."

msgctxt "flooring_angles description"
msgid "A list of integer line directions to use when the bottom surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Een lijst met lijnrichtingen in hele getallen om te gebruiken wanneer de onderste skinlagen het lijn- of zigzagpatroon gebruiken. Elementen uit de lijst worden opeenvolgend gebruikt naarmate de lagen vorderen en wanneer het einde van de lijst is bereikt, begint het opnieuw bij het begin. De lijstitems worden gescheiden door komma's en de hele lijst staat tussen vierkante haakjes. Default is een lege lijst, hetgeen inhoudt dat de traditionele standaardhoeken worden gebruikt (45 en 135 graden)."

msgctxt "acceleration_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Acceleration"
msgstr "Acceleratie onderzijde binnenwand"

msgctxt "jerk_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Jerk"
msgstr "Jerk onderzijde binnenwand"

msgctxt "speed_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Speed"
msgstr "Snelheid onderzijde binnenwand"

msgctxt "wall_x_material_flow_flooring label"
msgid "Bottom Surface Inner Wall(s) Flow"
msgstr "Flow onderzijde binnenwand(en)"

msgctxt "acceleration_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Acceleration"
msgstr "Acceleratie onderzijde buitenwand"

msgctxt "wall_0_material_flow_flooring label"
msgid "Bottom Surface Outer Wall Flow"
msgstr "Flow onderzijde buitenwand"

msgctxt "jerk_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Jerk"
msgstr "Jerk onderzijde buitenwand"

msgctxt "speed_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Speed"
msgstr "Snelheid onderzijde buitenwand"

msgctxt "acceleration_flooring label"
msgid "Bottom Surface Skin Acceleration"
msgstr "Acceleratie skin onderzijde"

msgctxt "flooring_extruder_nr label"
msgid "Bottom Surface Skin Extruder"
msgstr "Extruder skin onderzijde"

msgctxt "flooring_material_flow label"
msgid "Bottom Surface Skin Flow"
msgstr "Flow skin onderzijde"

msgctxt "jerk_flooring label"
msgid "Bottom Surface Skin Jerk"
msgstr "Jerk skin onderzijde"

msgctxt "flooring_layer_count label"
msgid "Bottom Surface Skin Layers"
msgstr "Lagen skin onderzijde"

msgctxt "flooring_angles label"
msgid "Bottom Surface Skin Line Directions"
msgstr "Lijnrichtingen skin onderzijde"

msgctxt "flooring_line_width label"
msgid "Bottom Surface Skin Line Width"
msgstr "Lijnbreedte skin onderzijde"

msgctxt "flooring_pattern label"
msgid "Bottom Surface Skin Pattern"
msgstr "Patroon skin onderzijde"

msgctxt "speed_flooring label"
msgid "Bottom Surface Skin Speed"
msgstr "Snelheid skin onderzijde"

msgctxt "flooring_pattern option concentric"
msgid "Concentric"
msgstr "Concentrisch"

msgctxt "variant_name"
msgid "Extruder"
msgstr "Extruder"

msgctxt "wall_x_material_flow_flooring description"
msgid "Flow compensation on bottom surface wall lines for all wall lines except the outermost one."
msgstr "Flowcompensatie voor wandlijnen onderzijde voor alle wandlijnen behalve de buitenste."

msgctxt "flooring_material_flow description"
msgid "Flow compensation on lines of the areas at the bottom of the print."
msgstr "Flowcompensatie voor lijnen van de gebieden aan de onderkant van de print."

msgctxt "wall_0_material_flow_flooring description"
msgid "Flow compensation on the bottom surface outermost wall line."
msgstr "Flowcompensatie voor de buitenste wandlijn van de onderkant."

msgctxt "machine_gcode_flavor option Cheetah"
msgid "Griffin+Cheetah"
msgstr "Griffin+Cheetah"

msgctxt "retraction_combing_avoid_distance label"
msgid "Inside Travel Avoid Distance"
msgstr "Inside Travel Avoid Distance"

msgctxt "flooring_pattern option lines"
msgid "Lines"
msgstr "Lijnen"

msgctxt "cool_min_layer_time_overhang label"
msgid "Minimum Layer Time with Overhang"
msgstr "Minimale laagtijd met overstek"

msgctxt "cool_min_layer_time_overhang_min_segment_length label"
msgid "Minimum Overhang Segment Length"
msgstr "Minimale segmentlengte overstek"

msgctxt "flooring_monotonic label"
msgid "Monotonic Bottom Surface Order"
msgstr "Volgorde monotone onderzijde"

msgctxt "wall_0_deceleration label"
msgid "Outer Wall End Deceleration"
msgstr "Deceleratie einde buitenwand"

msgctxt "wall_0_acceleration label"
msgid "Outer Wall Start Acceleration"
msgstr "Acceleratie begin buitenwand"

msgctxt "wall_overhang_speed_factors label"
msgid "Overhanging Wall Speeds"
msgstr "Snelheden overstekwand"

msgctxt "wall_overhang_speed_factors description"
msgid "Overhanging walls will be printed at a percentage of their normal print speed. You can specify multiple values, so that even more overhanging walls will be printed even slower, e.g. by setting [75, 50, 25]"
msgstr "Overstekwanden worden geprint op een percentage van de normale afdruksnelheid. U kunt meerdere waarden opgeven, zodat verder uitstekende wanden nog langzamer worden afgedrukt, bijv. door [75, 50, 25] in te stellen."

msgctxt "material_pressure_advance_factor label"
msgid "Pressure advance factor"
msgstr "Drukvoortgangsfactor"

msgctxt "variant_name"
msgid "Print Core"
msgstr "Printkern"

msgctxt "flooring_monotonic description"
msgid "Print bottom surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Print de lijnen van de onderkant af in een volgorde die ervoor zorgt dat ze altijd overlappen met aangrenzende lijnen in één richting. Dit kost iets meer tijd om te printen, maar zorgt ervoor dat platte vlakken er consistenter uitzien."

msgctxt "machine_start_gcode_first label"
msgid "Start GCode must be first"
msgstr "Start GCode moet eerst zijn"

msgctxt "acceleration_flooring description"
msgid "The acceleration with which bottom surface skin layers are printed."
msgstr "De acceleratie waarmee de skinlagen van de onderkant worden geprint."

msgctxt "acceleration_wall_x_flooring description"
msgid "The acceleration with which the bottom surface inner walls are printed."
msgstr "De acceleratie waarmee de binnenwanden van de onderkant worden geprint."

msgctxt "acceleration_wall_0_flooring description"
msgid "The acceleration with which the bottom surface outermost walls are printed."
msgstr "De acceleratie waarmee de buitenste wanden van de onderkant worden geprint."

msgctxt "machine_head_with_fans_polygon description"
msgid "The dimensions of the print head used to determine 'Safe Model Distance' when printing 'One at a Time'. These numbers relate to the centerline of the first extruder nozzle. Left of the nozzle is 'X Min' and must be negative.  Rear of the nozzle is 'Y Min' and must be negative.  X Max (right) and Y Max (front) are positive numbers.  Gantry height is the dimension from the build plate to the X gantry beam."
msgstr "De afmetingen van de printkop die worden gebruikt om de 'veilige modelafstand' te bepalen bij 'eén voor één printen'. Deze getallen hebben betrekking op de middellijn van de eerste spuitmond van de extruder. Links van de spuitmond is ‘X Min’ en moet negatief zijn.  De achterkant van de spuitmond is ‘Y Min’ en moet negatief zijn. X Max (rechts) en Y Max (voorzijde) zijn positieve getallen. Portaalhoogte is de afstand van de bouwplaat tot de X-portaalbalk."

msgctxt "retraction_combing_avoid_distance description"
msgid "The distance between the nozzle and already printed outer walls when travelling inside a model."
msgstr "De afstand tussen de spuitmond en de al geprinte buitenwanden bij het bewegen binnen een model."

msgctxt "flooring_extruder_nr description"
msgid "The extruder train used for printing the bottom most skin. This is used in multi-extrusion."
msgstr "De extrudertrein wordt gebruikt voor het afdrukken van de skin helemaal onderaan. Dit wordt gebruikt bij multi-extrusie."

msgctxt "jerk_flooring description"
msgid "The maximum instantaneous velocity change with which bottom surface skin layers are printed."
msgstr "De maximale directe snelheidsverandering waarmee skinlagen aan de onderkant worden geprint."

msgctxt "jerk_wall_x_flooring description"
msgid "The maximum instantaneous velocity change with which the bottom surface inner walls are printed."
msgstr "De maximale directe snelheidsverandering waarmee de binnenwanden aan de onderkant worden geprint."

msgctxt "jerk_wall_0_flooring description"
msgid "The maximum instantaneous velocity change with which the bottom surface outermost walls are printed."
msgstr "De maximale directe snelheidsverandering waarmee de buitenwanden aan de onderkant worden geprint."

msgctxt "cool_min_layer_time_overhang description"
msgid "The minimum time spent in a layer that contains overhanging extrusions. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr "De minimale tijd die wordt doorgebracht in een laag met overhangende uitsteeksels. Dit dwingt de printer langzamer te gaan, zodat de hier ingestelde tijd in ieder geval in één laag wordt gespendeerd. Zo kan het geprinte materiaal goed afkoelen voordat de volgende laag wordt geprint. Lagen kunnen nog steeds korter duren dan de minimale laagtijd als kop optillen is uitgeschakeld en de minimale snelheid anders zou worden overschreden."

msgctxt "flooring_layer_count description"
msgid "The number of bottom most skin layers. Usually only one bottom most layer is sufficient to generate higher quality bottom surfaces."
msgstr "Het aantal onderste skinlagen. Meestal is slechts één onderste laag voldoende om bodemoppervlakken van hogere kwaliteit te genereren."

msgctxt "flooring_pattern description"
msgid "The pattern of the bottom most layers."
msgstr "Het patroon van de onderste lagen."

msgctxt "speed_flooring description"
msgid "The speed at which bottom surface skin layers are printed."
msgstr " De snelheid waarmee de onderste skinlagen worden geprint."

msgctxt "speed_wall_x_flooring description"
msgid "The speed at which the bottom surface inner walls are printed."
msgstr "De snelheid waarmee de binnenwanden van de onderkant worden geprint."

msgctxt "speed_wall_0_flooring description"
msgid "The speed at which the bottom surface outermost wall is printed."
msgstr "De snelheid waarmee de buitenwand van de onderkant worden geprint."

msgctxt "machine_start_gcode_first description"
msgid "This setting controls if the start-gcode is forced to always be the first g-code. Without this option other g-code, such as a T0 can be inserted before the start g-code."
msgstr "Deze instelling bepaalt of de start-gcode wordt gedwongen om altijd de eerste g-code te zijn. Zonder deze optie kan een andere g-code, zoals een T0, voor de start-gcode worden ingevoerd."

msgctxt "material_pressure_advance_factor description"
msgid "Tuning factor for pressure advance, which is meant to synchronize extrusion with motion"
msgstr "Afstemmingsfactor voor drukvoortgang, bedoeld om extrusie met beweging te synchroniseren"

msgctxt "cool_min_layer_time_overhang_min_segment_length description"
msgid "When trying to apply the minimum layer time specific for overhanging layers, it will be applied only if at least one consecutive overhanging extrusion move is longer than this value."
msgstr "Indien u de minimale laagtijd specifiek voor overhangende lagen probeert toe te passen, wordt deze alleen toegepast als ten minste één opeenvolgende overhangende extrusiebeweging langer is dan deze waarde."

msgctxt "flooring_line_width description"
msgid "Width of a single line of the areas at the bottom of the print."
msgstr "Breedte van een enkele lijn van de gebieden aan de onderkant van de print."

msgctxt "flooring_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"
