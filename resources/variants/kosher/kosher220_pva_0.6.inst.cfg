[general]
definition = kosher
name = PVA 0.6 mm Nozzle
version = 4

[metadata]
hardware_type = nozzle
setting_version = 25
type = variant

[values]
gradual_support_infill_steps = 2
machine_nozzle_size = 0.6
support_angle = 45
support_bottom_height = =layer_height * 2
support_bottom_pattern = zigzag
support_bottom_stair_step_height = =layer_height
support_infill_rate = 50
support_infill_sparse_thickness = =(layer_height * 2) if (layer_height * 2) <= 0.75 * machine_nozzle_size else layer_height
support_interface_density = 100
support_interface_enable = True
support_interface_height = =layer_height * 5
support_interface_pattern = concentric
support_join_distance = 3
support_offset = 3
support_pattern = triangles
support_use_towers = False
support_xy_distance = =machine_nozzle_size / 2
support_xy_distance_overhang = =machine_nozzle_size / 2
support_z_distance = 0

