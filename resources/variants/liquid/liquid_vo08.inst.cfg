[general]
definition = liquid
name = VO 0.8
version = 4

[metadata]
hardware_type = nozzle
setting_version = 25
type = variant

[values]
acceleration_enabled = True
acceleration_print = 4000
brim_width = 7
cool_fan_speed = 7
cool_fan_speed_max = 100
cool_min_speed = 5
default_material_print_temperature = 200
infill_before_walls = False
infill_line_width = =round(line_width * 0.6 / 0.7, 2)
infill_overlap = 0
infill_pattern = triangles
infill_wipe_dist = 0
jerk_enabled = True
jerk_print = 15
jerk_topbottom = =math.ceil(jerk_print * 15 / 15)
jerk_wall = =math.ceil(jerk_print * 15 / 15)
jerk_wall_0 = =math.ceil(jerk_wall * 15 / 15)
line_width = =machine_nozzle_size
machine_min_cool_heat_time_window = 15
machine_nozzle_cool_down_speed = 0.85
machine_nozzle_heat_up_speed = 1.5
machine_nozzle_id = VO 0.8
machine_nozzle_size = 0.8
machine_nozzle_tip_outer_diameter = 2.0
material_final_print_temperature = =material_print_temperature - 10
material_initial_print_temperature = =material_print_temperature - 5
material_standby_temperature = 100
multiple_mesh_overlap = 0
prime_tower_enable = False
prime_tower_wipe_enabled = True
retract_at_layer_change = =not magic_spiralize
retraction_amount = 3
retraction_count_max = 25
retraction_extrusion_window = 1
retraction_hop = 2
retraction_hop_only_when_collides = True
retraction_min_travel = =line_width * 2
skin_overlap = 5
speed_layer_0 = 20
speed_print = 35
speed_topbottom = =math.ceil(speed_print * 25 / 35)
speed_wall_0 = =math.ceil(speed_wall * 25 / 30)
support_angle = 60
support_bottom_distance = =support_z_distance / 2
support_pattern = zigzag
support_top_distance = =support_z_distance
support_z_distance = =layer_height * 2
switch_extruder_prime_speed = 20
switch_extruder_retraction_amount = 16.5
top_bottom_thickness = 1.4
wall_0_inset = 0
wall_line_width_0 = =wall_line_width
wall_line_width_x = =wall_line_width
wall_thickness = 2

