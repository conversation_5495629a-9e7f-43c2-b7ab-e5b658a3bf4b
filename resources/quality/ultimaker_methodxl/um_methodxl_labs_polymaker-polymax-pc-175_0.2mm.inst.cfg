[general]
definition = ultimaker_methodxl
name = Fast - Experimental
version = 4

[metadata]
is_experimental = True
material = polymaker_polymax_pc_175
quality_type = draft
setting_version = 25
type = quality
variant = LABS
weight = -2

[values]
cool_fan_enabled = True
cool_fan_full_layer = 6
cool_fan_speed_max = 100
cool_min_layer_time = 8
cool_min_layer_time_fan_speed_max = 5
cool_min_speed = 10
raft_airgap = 0.22
raft_interface_fan_speed = 0
raft_interface_line_spacing = 0.7
raft_interface_line_width = 0.55
raft_interface_speed = 25
raft_interface_thickness = 0.25
raft_surface_fan_speed = 0
raft_surface_speed = 50
raft_surface_thickness = 0.25
speed_print = 50
speed_wall_0 = 30
speed_wall_x = =speed_wall
support_angle = 52
support_bottom_distance = =layer_height
support_bottom_enable = False
support_bottom_stair_step_height = 0.3
support_fan_enable = True
support_infill_angles = [45,45,45,45,45,45,45,45,45,45,45,45,45,45,45,135,135,135,135,135,135,135,135,135,135,135,135,135,135,135]
support_infill_rate = 10
support_interface_density = 83
support_interface_height = =layer_height * 4
support_interface_offset = 1.2
support_interface_wall_count = 0
support_line_width = 0.35
support_material_flow = 90
support_offset = 1.5
support_roof_density = =extruderValue(support_roof_extruder_nr, 'support_interface_density')
support_roof_height = =support_interface_height
support_roof_wall_count = 1
support_supported_skin_fan_speed = 100
support_xy_distance = 0.35
support_xy_overrides_z = xy_overrides_z
support_z_distance = 0.22
top_skin_expand_distance = 2.4
wall_overhang_angle = 30
wall_overhang_speed_factors = [40]

