[general]
definition = ultimaker_methodx
name = Fast
version = 4

[metadata]
material = ultimaker_sr30_175
quality_type = draft
setting_version = 25
type = quality
variant = 2XA
weight = -2

[values]
brim_replaces_support = False
cool_fan_enabled = False
cool_min_temperature = =material_print_temperature
raft_airgap = 0.0
raft_interface_flow = 110
raft_interface_infill_overlap = 25
raft_interface_line_width = 0.7
raft_interface_speed = =speed_print * 5/9
raft_interface_z_offset = -0.1
raft_surface_speed = =speed_print
retract_at_layer_change = True
retraction_amount = 0.5
retraction_min_travel = 5
speed_prime_tower = =speed_print * 1/3
speed_print = 90
speed_roofing = =speed_print * 5/9
speed_support = =speed_print
speed_support_bottom = =speed_print * 1/3
speed_support_interface = =speed_print * 5/9
speed_topbottom = =speed_print * 5/9
speed_wall = =speed_print * 5/9
speed_wall_0 = =speed_wall * 4/5
speed_wall_x = =speed_wall
support_bottom_wall_count = 5
support_conical_angle = 20
support_conical_enabled = True
support_conical_min_width = 20
support_fan_enable = False
support_interface_offset = 0
support_interface_wall_count = 2
support_roof_height = =5*layer_height

