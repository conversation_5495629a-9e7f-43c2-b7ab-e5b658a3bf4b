[general]
definition = ultimaker_methodx
name = Fast
version = 4

[metadata]
material = ultimaker_abscf_175
quality_type = draft
setting_version = 25
type = quality
variant = LABS
weight = -2

[values]
cool_fan_enabled = =extruder_nr == support_extruder_nr
cool_fan_speed = 0
cool_fan_speed_0 = 0
cool_fan_speed_max = 100
cool_min_layer_time = 6
cool_min_layer_time_fan_speed_max = 11
cool_min_speed = 6
cool_min_temperature = 250
raft_airgap = =0.15 if extruder_nr == support_extruder_nr else 0
raft_base_wall_count = =2 if extruder_nr == support_extruder_nr else 1
raft_surface_flow = =110 if extruder_nr == support_extruder_nr else 100
raft_surface_speed = =90 if extruder_nr == support_extruder_nr else 50
raft_surface_thickness = =0.25 if extruder_nr == support_extruder_nr else 0.2
raft_surface_z_offset = =-0.075 if extruder_nr == support_extruder_nr else 0
skin_overlap = 10
speed_layer_0 = =speed_print * 7/24
speed_prime_tower = =speed_print * 1/4
speed_print = 120.0
speed_roofing = =speed_print * 11/24
speed_support = =speed_print
speed_topbottom = =speed_print * 17/24
speed_wall_0 = =speed_print * 9/24
speed_wall_x = =speed_print * 13/24
support_angle = 52
support_bottom_density = 24
support_bottom_distance = =layer_height
support_bottom_enable = False
support_bottom_line_width = 0.6
support_bottom_stair_step_height = 0
support_fan_enable = False
support_infill_angles = [ 45,45,45,45,45,45,45,45,45,45,45,45,135,135,135,135,135,135,135,135,135,135,135,135]
support_infill_rate = 15.0
support_line_width = 0.35
support_material_flow = 90
support_roof_density = 82
support_roof_line_width = 0.4
support_xy_distance = 0.3
support_xy_distance_overhang = 0.25
support_z_distance = 0.15
wall_overhang_angle = 30
wall_overhang_speed_factors = [50]

