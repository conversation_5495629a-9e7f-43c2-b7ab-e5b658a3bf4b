[general]
definition = dagoma_sigma_pro
name = Normal
version = 4

[metadata]
material = generic_pla
quality_type = h0.2
setting_version = 25
type = quality
variant = Brass 0.4mm
weight = 1

[values]
adhesion_type = brim
bridge_settings_enabled = True
brim_width = 2
coasting_enable = True
coasting_volume = 0.06
cool_fan_full_layer = 5
cool_min_layer_time = 7
cool_min_temperature = 195
infill_material_flow = 105
infill_sparse_density = 15
material_final_print_temperature = 200
material_flow_layer_0 = 100
material_initial_print_temperature = 200
material_print_temperature = 200
material_print_temperature_layer_0 = 200
material_standby_temperature = 195
ooze_shield_enabled = False
prime_tower_brim_enable = True
prime_tower_enable = True
prime_tower_min_volume = 44
prime_tower_size = 25
prime_tower_wipe_enabled = False
retraction_amount = 3.0
retraction_combing = infill
retraction_hop = 0.4
retraction_hop_after_extruder_switch_height = 0
retraction_hop_enabled = True
retraction_prime_speed = 45
retraction_retract_speed = 60
retraction_speed = 80
smooth_spiralized_contours = False
speed_infill = 80
speed_layer_0 = 22
speed_prime_tower = 70
speed_print = 35
speed_slowdown_layers = 3
speed_topbottom = 35
speed_travel = 180
speed_travel_layer_0 = 70
speed_wall_0 = 25
speed_wall_x = 35.0
speed_z_hop = 120
support_enable = False
support_structure = tree
support_tree_angle = 55
support_tree_top_rate = =30 if support_roof_enable else 10
switch_extruder_extra_prime_amount = 0
switch_extruder_prime_speed = 20
switch_extruder_retraction_amount = 80
switch_extruder_retraction_speed = =switch_extruder_retraction_speeds
switch_extruder_retraction_speeds = 80
travel_avoid_distance = 2.0
travel_avoid_other_parts = False
travel_avoid_supports = True
wall_0_material_flow = =wall_material_flow
wall_0_material_flow_layer_0 = 85
wall_thickness = 1.2
wall_transition_length = 0.4
wall_x_material_flow_layer_0 = 90

