[general]
definition = liquid
name = Extra Fine
version = 4

[metadata]
material = generic_abs
quality_type = high
setting_version = 25
type = quality
variant = VO 0.4
weight = 1

[values]
cool_min_speed = 12
infill_line_width = =round(line_width * 0.4 / 0.35, 2)
machine_nozzle_cool_down_speed = 0.8
machine_nozzle_heat_up_speed = 1.5
material_final_print_temperature = =material_print_temperature - 20
material_initial_print_temperature = =material_print_temperature - 15
material_print_temperature = =default_material_print_temperature + 5
prime_tower_enable = False
speed_infill = =math.ceil(speed_print * 40 / 50)
speed_layer_0 = =math.ceil(speed_print * 20 / 50)
speed_print = 50
speed_topbottom = =math.ceil(speed_print * 30 / 50)
speed_wall = =math.ceil(speed_print * 30 / 50)

