[general]
definition = liquid
name = Fast
version = 4

[metadata]
material = generic_pp
quality_type = draft
setting_version = 25
type = quality
variant = VO 0.8
weight = -2

[values]
brim_width = 25
cool_min_layer_time_fan_speed_max = 6
cool_min_speed = 17
infill_before_walls = True
infill_line_width = =round(line_width * 0.7 / 0.8, 2)
infill_pattern = tetrahedral
jerk_prime_tower = =math.ceil(jerk_print * 25 / 25)
jerk_support = =math.ceil(jerk_print * 25 / 25)
jerk_wall_0 = =math.ceil(jerk_wall * 15 / 25)
material_bed_temperature_layer_0 = =material_bed_temperature
material_print_temperature = =default_material_print_temperature - 2
material_print_temperature_layer_0 = =default_material_print_temperature + 2
material_standby_temperature = 100
multiple_mesh_overlap = 0.2
prime_tower_enable = True
prime_tower_flow = 100
prime_tower_min_volume = 10
retract_at_layer_change = False
retraction_count_max = 12
retraction_extra_prime_amount = 0.5
retraction_hop = 0.5
retraction_min_travel = 1.5
retraction_prime_speed = 15
skin_line_width = =round(line_width * 0.78 / 0.8, 2)
speed_wall_x = =math.ceil(speed_wall * 30 / 30)
support_bottom_distance = =support_z_distance
support_line_width = =round(line_width * 0.7 / 0.8, 2)
support_offset = =line_width
switch_extruder_prime_speed = 15
switch_extruder_retraction_amount = 20
switch_extruder_retraction_speeds = 45
top_bottom_thickness = 1.6
top_skin_expand_distance = =line_width * 2
wall_0_wipe_dist = =line_width * 2
wall_line_width_x = =round(line_width * 0.8 / 0.8, 2)
wall_thickness = 1.6

