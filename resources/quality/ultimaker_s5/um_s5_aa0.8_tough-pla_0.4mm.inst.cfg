[general]
definition = ultimaker_s5
name = Sprint
version = 4

[metadata]
material = generic_tough_pla
quality_type = superdraft
setting_version = 25
type = quality
variant = AA 0.8
weight = -4

[values]
gradual_infill_step_height = =3 * layer_height
infill_pattern = ='zigzag' if infill_sparse_density > 80 else 'cubic'
machine_nozzle_cool_down_speed = 0.75
machine_nozzle_heat_up_speed = 1.6
material_print_temperature = =default_material_print_temperature + 15
prime_tower_enable = False
speed_infill = =math.ceil(speed_print * 30 / 30)
speed_print = 30
speed_topbottom = =math.ceil(speed_print * 20 / 30)
speed_wall = =math.ceil(speed_print * 25 / 30)
speed_wall_0 = =math.ceil(speed_print * 20 / 30)
support_angle = 70
top_bottom_thickness = =layer_height * 4

