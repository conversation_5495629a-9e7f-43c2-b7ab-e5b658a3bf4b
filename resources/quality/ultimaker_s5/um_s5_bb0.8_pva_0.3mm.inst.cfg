[general]
definition = ultimaker_s5
name = Extra Fast
version = 4

[metadata]
material = generic_pva
quality_type = verydraft
setting_version = 25
type = quality
variant = BB 0.8
weight = -3

[values]
acceleration_prime_tower = 1500
brim_replaces_support = False
build_volume_temperature = =70 if extruders_enabled_count > 1 and (not support_enable or extruder_nr != support_extruder_nr) else 35
cool_fan_enabled = =not (support_enable and (extruder_nr == support_infill_extruder_nr))
default_material_bed_temperature = =0 if extruders_enabled_count > 1 and (not support_enable or extruder_nr != support_extruder_nr) else 60
initial_layer_line_width_factor = 150
material_print_temperature = =default_material_print_temperature + 5
minimum_support_area = 4
retraction_count_max = 5
skirt_brim_minimal_length = =min(2000, 175 / (layer_height * line_width))
speed_prime_tower = 25
speed_support = 50
support_infill_sparse_thickness = 0.3
support_interface_enable = True

