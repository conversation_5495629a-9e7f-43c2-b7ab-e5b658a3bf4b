[general]
definition = ultimaker_s3
name = Fine
version = 4

[metadata]
material = ultimaker_petg
quality_type = normal
setting_version = 25
type = quality
variant = AA 0.25
weight = 0

[values]
acceleration_infill = =acceleration_print
acceleration_ironing = 1000
acceleration_layer_0 = =acceleration_wall_0
acceleration_print = 3500
acceleration_roofing = =acceleration_wall_0
acceleration_topbottom = =acceleration_wall
acceleration_wall = =acceleration_infill
acceleration_wall_0 = 1500
acceleration_wall_x = =acceleration_wall
bridge_skin_speed = =bridge_wall_speed
bridge_sparse_infill_max_density = 50
bridge_wall_speed = 30
cool_min_layer_time = 4
gradual_flow_discretisation_step_size = 0.2
gradual_flow_enabled = True
infill_pattern = ='zigzag' if infill_sparse_density > 80 else 'grid'
infill_sparse_density = 15
jerk_infill = =jerk_print
jerk_layer_0 = =jerk_wall_0
jerk_print = =max(30, speed_print/2)
jerk_roofing = =jerk_wall_0
jerk_topbottom = =jerk_wall
jerk_wall = =jerk_infill
jerk_wall_0 = =max(30, speed_wall_0/2)
machine_nozzle_cool_down_speed = 1.4
machine_nozzle_heat_up_speed = 1.7
material_extrusion_cool_down_speed = 0.7
max_flow_acceleration = 1
optimize_wall_printing_order = False
prime_tower_enable = False
retraction_amount = 8
retraction_prime_speed = 15
retraction_speed = 45
small_skin_on_surface = False
small_skin_width = 4
speed_infill = =speed_print
speed_ironing = 20
speed_layer_0 = 30
speed_prime_tower = =speed_wall_0
speed_print = 100
speed_roofing = =math.ceil(speed_wall*(45/100))
speed_support_interface = =speed_wall_0
speed_topbottom = =speed_print
speed_wall = =speed_infill
speed_wall_0 = =math.ceil(speed_wall*(20/100))
speed_wall_x = =speed_wall
speed_wall_x_roofing = =speed_roofing
support_bottom_distance = =support_z_distance
support_interface_enable = True
support_structure = tree
support_top_distance = =support_z_distance
support_z_distance = 0.3
top_bottom_thickness = =max(1.2 , layer_height * 6)
wall_0_wipe_dist = 0.8
zig_zaggify_infill = True

