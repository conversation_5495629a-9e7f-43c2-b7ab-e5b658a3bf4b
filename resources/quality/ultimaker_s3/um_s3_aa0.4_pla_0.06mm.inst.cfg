[general]
definition = ultimaker_s3
name = Extra Fine
version = 4

[metadata]
material = generic_pla
quality_type = high
setting_version = 25
type = quality
variant = AA 0.4
weight = 1

[values]
machine_nozzle_cool_down_speed = 0.75
machine_nozzle_heat_up_speed = 1.6
material_print_temperature = =default_material_print_temperature - 5
raft_airgap = 0.25
retraction_prime_speed = =retraction_speed
speed_print = 50
speed_topbottom = =math.ceil(speed_print * 35 / 50)
speed_wall = =math.ceil(speed_print * 35 / 50)
support_bottom_distance = =support_z_distance
support_interface_enable = True
support_top_distance = =support_z_distance
support_z_distance = =math.ceil(0.3/layer_height)*layer_height
top_bottom_thickness = 1

