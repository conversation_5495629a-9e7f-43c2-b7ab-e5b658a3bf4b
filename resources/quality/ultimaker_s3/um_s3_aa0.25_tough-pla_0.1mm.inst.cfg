[general]
definition = ultimaker_s3
name = Fine
version = 4

[metadata]
material = generic_tough_pla
quality_type = normal
setting_version = 25
type = quality
variant = AA 0.25
weight = 0

[values]
brim_width = 8
infill_pattern = ='zigzag' if infill_sparse_density > 80 else 'grid'
machine_nozzle_cool_down_speed = 0.9
machine_nozzle_heat_up_speed = 1.4
material_print_temperature = =default_material_print_temperature - 5
speed_print = 30
speed_topbottom = =math.ceil(speed_print * 20 / 30)
speed_wall = =math.ceil(speed_print * 25 / 30)
speed_wall_0 = =math.ceil(speed_print * 20 / 30)
support_bottom_distance = =support_z_distance
support_interface_enable = True
support_top_distance = =support_z_distance
support_z_distance = =math.ceil(0.3/layer_height)*layer_height
top_bottom_thickness = 0.72
wall_0_inset = 0.015
wall_0_wipe_dist = 0.25

