[general]
definition = ultimaker_s3
name = Sprint - Experimental
version = 4

[metadata]
is_experimental = True
material = generic_pc
quality_type = superdraft
setting_version = 25
type = quality
variant = AA 0.8
weight = -4

[values]
brim_width = 14
material_print_temperature = =default_material_print_temperature - 10
raft_airgap = 0.5
speed_infill = =math.ceil(speed_print * 37 / 50)
speed_print = 50
speed_topbottom = =math.ceil(speed_print * 25 / 50)
speed_wall = =math.ceil(speed_print * 37 / 50)
speed_wall_0 = =math.ceil(speed_wall * 30 / 40)

