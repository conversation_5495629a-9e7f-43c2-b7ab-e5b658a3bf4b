[general]
definition = ultimaker_s3
name = Extra Fast
version = 4

[metadata]
material = generic_pp
quality_type = verydraft
setting_version = 25
type = quality
variant = AA 0.8
weight = -3

[values]
brim_width = 25
infill_pattern = ='zigzag' if infill_sparse_density > 80 else 'tetrahedral'
material_final_print_temperature = =material_print_temperature - 10
material_initial_print_temperature = =material_print_temperature - 10
material_print_temperature = =default_material_print_temperature + 13
multiple_mesh_overlap = 0.2
prime_tower_enable = True
prime_tower_flow = 100
prime_tower_min_volume = 15
retraction_count_max = 15
retraction_extra_prime_amount = 0.5
retraction_hop = 0.5
retraction_prime_speed = 15
speed_wall_x = =math.ceil(speed_wall * 30 / 30)
switch_extruder_prime_speed = 15
switch_extruder_retraction_amount = 20
switch_extruder_retraction_speeds = 45
top_bottom_thickness = 1.6
top_skin_expand_distance = =line_width * 2
wall_0_wipe_dist = =line_width * 2

