[general]
definition = ultimaker_s3
name = Extra Fast - Experimental
version = 4

[metadata]
is_experimental = True
material = generic_tough_pla
quality_type = verydraft
setting_version = 25
type = quality
variant = AA 0.4
weight = -3

[values]
acceleration_print = 2000
acceleration_topbottom = 1000
acceleration_wall = 1500
acceleration_wall_0 = 1000
infill_pattern = ='zigzag' if infill_sparse_density > 80 else 'triangles'
infill_sparse_density = 15
machine_nozzle_cool_down_speed = 0.75
machine_nozzle_heat_up_speed = 1.6
material_print_temperature = =default_material_print_temperature + 5
prime_tower_enable = False
raft_airgap = 0.25
retraction_prime_speed = =retraction_speed
speed_print = 50
speed_wall = 50
support_bottom_distance = =support_z_distance
support_interface_enable = True
support_top_distance = =support_z_distance
support_z_distance = =math.ceil(0.3/layer_height)*layer_height
top_bottom_thickness = 1.2
wall_line_width_0 = =line_width * (1 + magic_spiralize * 0.25)

