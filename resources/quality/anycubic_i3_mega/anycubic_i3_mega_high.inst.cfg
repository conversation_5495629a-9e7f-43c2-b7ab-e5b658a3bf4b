[general]
definition = anycubic_i3_mega
name = High
version = 4

[metadata]
global_quality = True
quality_type = high
setting_version = 25
type = quality
weight = 1

[values]
acceleration_enabled = True
acceleration_print = 1800
acceleration_travel = 3000
adhesion_type = skirt
brim_width = 4.0
cool_fan_full_at_height = 0.5
cool_fan_speed_max = 100
infill_overlap = 15
infill_pattern = zigzag
infill_sparse_density = 25
initial_layer_line_width_factor = 140
jerk_enabled = True
jerk_print = 8
jerk_travel = 10
layer_height = 0.1
layer_height_0 = 0.1
retract_at_layer_change = False
retraction_amount = 6
retraction_hop = 0.075
retraction_hop_enabled = True
retraction_hop_only_when_collides = True
retraction_min_travel = 1.5
retraction_speed = 40
skirt_brim_speed = 40
skirt_gap = 5
skirt_line_count = 3
speed_infill = =speed_print
speed_print = 50
speed_support = 30
speed_topbottom = =math.ceil(speed_print * 20 / 50)
speed_travel = 50
speed_wall = =speed_print
speed_wall_x = =speed_print
support_angle = 60
support_enable = True
support_interface_enable = True
support_pattern = triangles
support_roof_enable = True
support_type = everywhere
support_use_towers = False
support_xy_distance = 0.7
top_bottom_thickness = 1.2
wall_thickness = 1.2

