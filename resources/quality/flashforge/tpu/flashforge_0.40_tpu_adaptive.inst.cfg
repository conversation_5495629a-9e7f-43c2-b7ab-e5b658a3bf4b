[general]
definition = flashforge_base
name = Dynamic Quality
version = 4

[metadata]
material = generic_tpu
quality_type = adaptive
setting_version = 25
type = quality
variant = 0.4mm Nozzle

[values]
cool_fan_speed = 100
cool_fan_speed_0 = 0
material_bed_temperature = 35
material_bed_temperature_layer_0 = 40
material_print_temperature = 230
material_print_temperature_layer_0 = 235
speed_infill = =speed_print
speed_layer_0 = 10
speed_print = 15
speed_support = 15
speed_travel = 100

