[general]
definition = beamup_l
name = BeamUp L Extra Fine
version = 4

[metadata]
material = generic_pla
quality_type = high
setting_version = 25
type = quality
weight = 1

[values]
adhesion_type = brim
brim_line_count = 8
infill_before_walls = False
initial_layer_line_width_factor = 120.0
layer_height = 0.06
material_print_temperature = 195
material_print_temperature_layer_0 = 235
retraction_amount = 2
retraction_speed = 30
speed_infill = 45
speed_layer_0 = 25
speed_print = 45
speed_support_interface = 45
speed_topbottom = 45
speed_wall_0 = 35
speed_wall_x = 45
support_angle = 60
support_enable = True
support_infill_rate = 20
support_interface_enable = True
support_interface_height = 0.30
support_interface_pattern = zigzag
support_offset = 0.8
support_z_distance = 0.12
wall_thickness = 1.6
zig_zaggify_infill = True

