[general]
definition = ultimaker_method
name = Fast
version = 4

[metadata]
material = ultimaker_tough_pla_175
quality_type = draft
setting_version = 25
type = quality
variant = 1A
weight = -2

[values]
cool_fan_enabled = True
cool_fan_speed_0 = 0
cool_min_layer_time = 10
cool_min_speed = 5
cool_min_temperature = 205
infill_sparse_density = 15
material_print_temperature_layer_0 = =default_material_print_temperature + 15
min_bead_width = =line_width * 0.75
min_wall_line_width = =line_width * 0.75
raft_airgap = 0.22
raft_base_line_spacing = =4 if extruder_nr == raft_interface_extruder_nr else 2.8
raft_base_line_width = =1.2 if extruder_nr == raft_interface_extruder_nr else 1.4
raft_base_thickness = 0.6
raft_interface_flow = 105
raft_interface_infill_overlap = 25
raft_interface_layers = 1
raft_interface_line_spacing = 0.75
raft_interface_speed = 60
raft_interface_thickness = 0.2
raft_interface_z_offset = -0.1
raft_margin = 3
raft_speed = 15
raft_surface_flow = 103
raft_surface_infill_overlap = 25
raft_surface_line_spacing = 0.425
raft_surface_line_width = 0.4
raft_surface_speed = 90
raft_surface_z_offset = -0.1
seam_overhang_angle = 35
speed_layer_0 = 15
speed_print = 150
speed_roofing = =speed_print / 3
speed_support = =speed_print
speed_support_interface = 65
speed_topbottom = =speed_print * 0.6
speed_wall = =speed_print / 6
speed_wall_0 = =speed_wall
speed_wall_x = =speed_wall
support_angle = 50.0
support_bottom_distance = =support_z_distance
support_bottom_enable = False
support_infill_angles = [45 ]
support_infill_rate = 12
support_interface_density = 75
support_interface_material_flow = =support_material_flow
support_interface_offset = 0.8
support_interface_pattern = zigzag
support_line_width = =line_width * 0.75
support_material_flow = =material_flow * 0.9
support_offset = 1
support_pattern = zigzag
support_roof_density = =support_interface_density
support_roof_line_width = =line_width
support_top_distance = =support_z_distance
support_xy_distance = 0.35
support_xy_distance_overhang = 0.32
support_xy_overrides_z = xy_overrides_z
support_z_distance = 0.22
switch_extruder_retraction_amount = 1

