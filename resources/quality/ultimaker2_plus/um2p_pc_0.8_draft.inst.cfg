[general]
definition = ultimaker2_plus
name = Fast
version = 4

[metadata]
material = generic_pc
quality_type = extracoarse
setting_version = 25
type = quality
variant = 0.8 mm
weight = -2

[values]
adhesion_type = raft
brim_line_count = 10
cool_fan_speed = 50
cool_fan_speed_min = =cool_fan_speed * 25 / 50
infill_overlap = =0 if infill_sparse_density > 80 else 5
infill_sparse_density = 40
layer_0_z_overlap = 0.22
raft_airgap = 0.47
speed_print = 40
support_angle = 45
support_enable = True
support_z_distance = 0.26
top_bottom_thickness = 2.0

