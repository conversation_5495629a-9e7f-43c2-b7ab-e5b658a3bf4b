[general]
definition = fusion3
name = Normal Quality
version = 4

[metadata]
material = generic_abs
quality_type = normal
setting_version = 25
type = quality
variant = 0.4mm Nozzle

[values]
adhesion_type = brim
cool_fan_enabled = True
cool_fan_full_layer = 70
cool_fan_speed = 65
cool_fan_speed_min = 15
cool_min_layer_time = 3
cool_min_layer_time_fan_speed_max = 60
infill_sparse_density = 30
inset_direction = outside_in
layer_height = 0.2
layer_height_0 = 0.2
line_width = 0.45
material_bed_temperature = 110
material_flow = 98
material_print_temperature = 270
retract_at_layer_change = False
retraction_amount = 4
retraction_combing = noskin
retraction_combing_max_distance = 10
speed_print = 100
speed_travel_layer_0 = 100
support_angle = 45
support_enable = True
support_infill_rate = 25
top_bottom_thickness = 0.6
wall_thickness = 0.8
zig_zaggify_infill = True

