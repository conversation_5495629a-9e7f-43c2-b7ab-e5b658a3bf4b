[general]
definition = fusion3
name = Rough Quality
version = 4

[metadata]
material = generic_pla
quality_type = rough
setting_version = 25
type = quality
variant = 0.8mm Nozzle

[values]
adhesion_type = skirt
cool_fan_full_at_height = 1.0
cool_fan_speed = 100
cool_min_layer_time = 16
cool_min_speed = 30
infill_overlap = 20
infill_sparse_density = 30
inset_direction = outside_in
layer_height_0 = 0.44
line_width = 0.85
material_bed_temperature = 55
material_flow = 96
material_flow_layer_0 = 96
material_print_temperature = 240
material_print_temperature_layer_0 = 240
retract_at_layer_change = False
retraction_amount = 4
retraction_speed = 100
skirt_gap = 5
speed_infill = 40
speed_print = 60
speed_travel = 150
speed_travel_layer_0 = 100
speed_wall_x = 40
top_bottom_thickness = 1.4
wall_line_count = 2
xy_offset_layer_0 = -0.25

