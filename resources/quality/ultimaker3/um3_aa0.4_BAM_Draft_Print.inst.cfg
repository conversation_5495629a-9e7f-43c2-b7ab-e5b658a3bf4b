[general]
definition = ultimaker3
name = Fast
version = 4

[metadata]
material = generic_bam
quality_type = draft
setting_version = 25
type = quality
variant = AA 0.4
weight = -2

[values]
brim_replaces_support = False
machine_nozzle_cool_down_speed = 0.75
machine_nozzle_heat_up_speed = 1.6
material_print_temperature = =default_material_print_temperature + 5
prime_tower_enable = =min(extruderValues('material_surface_energy')) < 100
skin_overlap = 20
speed_topbottom = =math.ceil(speed_print * 35 / 70)
speed_wall = =math.ceil(speed_print * 50 / 70)
speed_wall_0 = =math.ceil(speed_wall * 35 / 50)
support_angle = 45
support_bottom_distance = =math.ceil(min(extruderValues('material_adhesion_tendency')) / 2) * layer_height
support_interface_density = =min(extruderValues('material_surface_energy'))
support_interface_enable = True
support_top_distance = =math.ceil(min(extruderValues('material_adhesion_tendency')) / 2) * layer_height
top_bottom_thickness = 1

