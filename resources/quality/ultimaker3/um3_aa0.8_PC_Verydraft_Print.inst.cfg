[general]
definition = ultimaker3
name = Extra Fast - Experimental
version = 4

[metadata]
is_experimental = True
material = generic_pc
quality_type = verydraft
setting_version = 25
type = quality
variant = AA 0.8
weight = -3

[values]
brim_width = 14
layer_height = 0.3
material_print_temperature = =default_material_print_temperature - 12
raft_airgap = 0.5
skin_overlap = 0
speed_print = 50
speed_topbottom = =math.ceil(speed_print * 25 / 50)
speed_wall = =math.ceil(speed_print * 40 / 50)
speed_wall_0 = =math.ceil(speed_wall * 30 / 40)

