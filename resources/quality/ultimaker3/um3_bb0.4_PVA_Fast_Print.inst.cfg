[general]
definition = ultimaker3
name = Normal
version = 4

[metadata]
material = generic_pva
quality_type = fast
setting_version = 25
type = quality
variant = BB 0.4
weight = -1

[values]
brim_replaces_support = False
cool_fan_enabled = =not (support_enable and (extruder_nr == support_infill_extruder_nr))
prime_tower_enable = False
retraction_count_max = 5
skin_overlap = 15
skirt_brim_minimal_length = =min(2000, 175/(layer_height*line_width))
support_infill_sparse_thickness = =2*layer_height
support_interface_enable = True

