[general]
definition = ultimaker3
name = Fine
version = 4

[metadata]
material = generic_pc
quality_type = normal
setting_version = 25
type = quality
variant = AA 0.4
weight = 0

[values]
adhesion_type = raft
brim_width = 20
infill_overlap = 0
infill_pattern = ='zigzag' if infill_sparse_density > 80 else 'triangles'
infill_wipe_dist = 0.1
machine_min_cool_heat_time_window = 15
machine_nozzle_cool_down_speed = 0.85
machine_nozzle_heat_up_speed = 1.5
material_print_temperature = =default_material_print_temperature - 10
multiple_mesh_overlap = 0
ooze_shield_angle = 40
prime_tower_enable = True
prime_tower_wipe_enabled = True
raft_airgap = 0.25
retraction_hop = 2
retraction_hop_only_when_collides = True
retraction_min_travel = 0.8
retraction_prime_speed = 15
skin_overlap = 30
speed_print = 50
speed_topbottom = =math.ceil(speed_print * 25 / 50)
speed_travel = 250
speed_wall = =math.ceil(speed_print * 40 / 50)
speed_wall_0 = =math.ceil(speed_wall * 25 / 40)
support_interface_density = 87.5
switch_extruder_prime_speed = 15
switch_extruder_retraction_amount = 20
switch_extruder_retraction_speeds = 35
wall_0_inset = 0

