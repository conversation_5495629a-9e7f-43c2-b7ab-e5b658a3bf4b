[general]
definition = ultimaker_factor4
name = Fast
version = 4

[metadata]
material = generic_pva
quality_type = draft
setting_version = 25
type = quality
variant = BB 0.8
weight = -2

[values]
acceleration_print = 1000.0
acceleration_support_bottom = 100
acceleration_support_interface = 1000
brim_replaces_support = False
build_volume_temperature = =40 if extruders_enabled_count > 1 else 35
default_material_bed_temperature = =0 if extruders_enabled_count > 1 else 60
gradual_flow_discretisation_step_size = 0.1
gradual_flow_enabled = True
gradual_support_infill_steps = 0
initial_layer_line_width_factor = 125
jerk_print = 10
material_flow_layer_0 = 90
max_flow_acceleration = 1
minimum_support_area = 4
prime_tower_flow = 90
prime_tower_min_volume = 15
retraction_min_travel = 5.0
retraction_prime_speed = 10.0
skin_material_flow = =material_flow * 0.93
speed_print = 30
support_angle = 45
support_infill_rate = 20
support_infill_sparse_thickness = =min(layer_height * 2, machine_nozzle_size * 3 / 4) if layer_height <= 0.15 / 0.4 * machine_nozzle_size else layer_height
support_interface_offset = 1
support_offset = 3
support_xy_distance = 2
support_z_distance = 0
switch_extruder_prime_speed = 10.0

