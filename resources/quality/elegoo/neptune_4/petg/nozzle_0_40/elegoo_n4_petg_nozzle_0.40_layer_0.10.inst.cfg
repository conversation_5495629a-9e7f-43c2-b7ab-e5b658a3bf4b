[general]
definition = elegoo_neptune_4
name = petg_noz0.40_lay0.10
version = 4

[metadata]
material = generic_petg
quality_type = Elegoo_layer_010
setting_version = 25
type = quality
variant = 0.40mm_Elegoo_Nozzle

[values]
brim_width = 6
cool_fan_speed_min = =cool_fan_speed*0.5
cool_min_layer_time = 10
cool_min_layer_time_fan_speed_max = 30
layer_0_z_overlap = =raft_airgap*0.6
material_shrinkage_percentage_xy = 100.2
raft_airgap = =0.35
raft_margin = 10

