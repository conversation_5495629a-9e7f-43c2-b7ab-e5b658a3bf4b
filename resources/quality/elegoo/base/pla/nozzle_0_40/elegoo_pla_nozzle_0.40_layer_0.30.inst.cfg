[general]
definition = elegoo_base
name = pla_noz0.40_lay0.30
version = 4

[metadata]
material = generic_pla
quality_type = Elegoo_layer_030
setting_version = 25
type = quality
variant = 0.40mm_Elegoo_Nozzle

[values]
acceleration_print = 2000
acceleration_topbottom = 1000
acceleration_wall = 1500
acceleration_wall_0 = 1000
infill_pattern = ='zigzag' if infill_sparse_density > 80 else 'triangles'
infill_sparse_density = 15
machine_nozzle_cool_down_speed = 0.75
machine_nozzle_heat_up_speed = 1.6
material_print_temperature = =default_material_print_temperature + 10
prime_tower_enable = False
raft_airgap = 0.25
retraction_prime_speed = =retraction_speed
skin_overlap = 20
speed_print = 50
speed_wall = 50
top_bottom_thickness = 0.9
wall_line_width_0 = =line_width * (1 + magic_spiralize * 0.25)

