[general]
definition = strateo3d
name = A
version = 4

[metadata]
material = emotiontech_acetate
quality_type = a
setting_version = 25
type = quality
variant = Standard 0.4
weight = 1

[values]
cool_fan_enabled = True
cool_fan_full_at_height = =layer_height_0 + 10 * layer_height
cool_fan_speed = 35
cool_fan_speed_max = 100
cool_min_layer_time = 11
cool_min_layer_time_fan_speed_max = 20
cool_min_speed = 10
layer_height_0 = =round(0.5*machine_nozzle_size, 2)
line_width = =machine_nozzle_size/machine_nozzle_size*0.4
material_flow = 98
material_print_temperature = =default_material_print_temperature
material_print_temperature_layer_0 = =default_material_print_temperature
prime_tower_enable = True
retraction_extra_prime_amount = 0.1
retraction_hop_only_when_collides = True
retraction_min_travel = =3*line_width
skin_overlap = 10
speed_layer_0 = =math.ceil(speed_print * 25/50)
speed_print = 50
speed_slowdown_layers = 2
speed_topbottom = =math.ceil(speed_print * 33/50)
speed_wall = =math.ceil(speed_print * 37/50)
speed_wall_0 = =math.ceil(speed_wall * 30/37)
support_angle = 60
support_bottom_distance = =support_z_distance
support_interface_density = 100
support_offset = 1
support_xy_distance = =line_width * 1.7
support_xy_distance_overhang = =wall_line_width_0
support_z_distance = =layer_height
wall_0_wipe_dist = =machine_nozzle_size/2
wall_line_width = =machine_nozzle_size/machine_nozzle_size*0.35
wall_line_width_x = =machine_nozzle_size/machine_nozzle_size*0.4

