[general]
definition = strateo3d
name = F
version = 4

[metadata]
material = emotiontech_tpu98a
quality_type = f
setting_version = 25
type = quality
variant = Standard 1.0 Experimental
weight = -1

[values]
cool_fan_enabled = True
cool_fan_full_at_height = =layer_height_0 + 6 * layer_height
cool_fan_speed = 50
cool_fan_speed_max = 100
cool_min_layer_time = 11
cool_min_layer_time_fan_speed_max = 20
cool_min_speed = 10
layer_height_0 = =round(0.75*machine_nozzle_size, 2)
line_width = =machine_nozzle_size/machine_nozzle_size*0.9
material_flow = 107
material_print_temperature = =default_material_print_temperature + 8
material_print_temperature_layer_0 = =default_material_print_temperature + 5
prime_tower_enable = True
retraction_extra_prime_amount = 0.3
retraction_hop_only_when_collides = False
retraction_min_travel = =2*line_width
skin_overlap = 5
speed_layer_0 = =math.ceil(speed_print * 20/35)
speed_print = 35
speed_slowdown_layers = 1
speed_topbottom = =math.ceil(speed_print * 23/35)
speed_wall = =math.ceil(speed_print * 35/35)
speed_wall_0 = =math.ceil(speed_wall * 27/35)
support_angle = 50
support_bottom_distance = =support_z_distance
support_interface_density = 100
support_offset = 0.7
support_xy_distance = =line_width * 2.5
support_xy_distance_overhang = =wall_line_width_0
support_z_distance = =layer_height
wall_0_wipe_dist = =machine_nozzle_size
wall_line_width = =machine_nozzle_size/machine_nozzle_size*0.9
wall_line_width_x = =machine_nozzle_size/machine_nozzle_size*0.9

