[general]
definition = ultimaker2_plus_connect
name = Normal
version = 4

[metadata]
material = generic_pc
quality_type = draft
setting_version = 25
type = quality
variant = 0.4 mm
weight = -1

[values]
cool_fan_speed = 50
cool_fan_speed_min = 0
infill_overlap = =0 if infill_sparse_density > 80 else 5
infill_sparse_density = 20
layer_0_z_overlap = 0.3
raft_airgap = 0.35
speed_infill = =math.ceil(speed_print * 45 / 45)
speed_print = 45
speed_topbottom = =math.ceil(speed_print * 30 / 45)
speed_wall_0 = =math.ceil(speed_print * 20 / 45)
speed_wall_x = =math.ceil(speed_print * 30 / 45)
support_angle = 45
support_enable = True
support_z_distance = 0.19

