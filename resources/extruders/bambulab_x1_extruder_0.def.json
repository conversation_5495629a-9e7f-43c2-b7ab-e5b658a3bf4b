{"version": 2, "name": "Extruder", "inherits": "fdmextruder", "metadata": {"machine": "bambulab_x1", "position": "0"}, "overrides": {"extruder_nr": {"default_value": 0}, "machine_extruder_change_duration": {"default_value": 29}, "machine_extruder_end_code": {"default_value": "M204 S9000\n\nG91 ; set relative positioning\nG1 Z3.0 F1200\nG90 ; back to abolute positioning\n\nG1 X70 F21000\nG1 Y245\nG1 Y265 F3000\nM400\nM106 P1 S0\nM106 P2 S0\n\n{if material_print_temperature > 142}\nM104 S{material_print_temperature}\n{endif}\n\nM620.11 S1 I{extruder_nr} E-18 F1200\nM400\n\nG1 X90 F3000\nG1 Y255 F4000\nG1 X100 F5000\nG1 X120 F15000\nG1 X20 Y50 F21000\nG1 Y-3\n\n;{if toolchange_count == 2}\n; get travel path for change filament\n;M620.1 X[travel_point_1_x] Y[travel_point_1_y] F21000 P0\n;M620.1 X[travel_point_2_x] Y[travel_point_2_y] F21000 P1\n;M620.1 X[travel_point_3_x] Y[travel_point_3_y] F21000 P2\n;{endif}\n\nM620.1 E F{material_flush_purge_speed} T{material_print_temperature, extruder_nr}\n"}, "machine_extruder_start_code": {"default_value": "M620.1 E F{material_flush_purge_speed} T{material_print_temperature, extruder_nr}\n\nG92 E0\n\n; always use highest temperature to flush\n{if material_type == 'PETG'}\nM109 S260\n{elsif material_type == 'PVA'}\nM109 S210\n{else}\nM109 S{material_print_temperature}\n{endif}\n\n{if not prime_tower_enable}\n\nM83\n; FLUSH_START\n; always use highest temperature to flush\nM400\nM1002 set_filament_type:UNKNOWN\nM109 S{material_print_temperature, extruder_nr}\nM106 P1 S60\nG1 E{material_flush_purge_length / 4.0} F{min(extruderValues('material_flush_purge_speed'))} ; do not need pulsatile flushing for start part\nG1 E{(material_flush_purge_length / 4.0) * 0.02} F50\nG1 E{(material_flush_purge_length / 4.0) * 0.23} F{min(extruderValues('material_flush_purge_speed'))}\nG1 E{(material_flush_purge_length / 4.0) * 0.02} F50\nG1 E{(material_flush_purge_length / 4.0) * 0.23} F{material_flush_purge_speed, extruder_nr}\nG1 E{(material_flush_purge_length / 4.0) * 0.02} F50\nG1 E{(material_flush_purge_length / 4.0) * 0.23} F{material_flush_purge_speed, extruder_nr}\nG1 E{(material_flush_purge_length / 4.0) * 0.02} F50\nG1 E{(material_flush_purge_length / 4.0) * 0.23} F{material_flush_purge_speed, extruder_nr}\n; FLUSH_END\nG1 E-{retraction_amount * 2} F1800\nG1 E{retraction_amount * 2} F300\nM400\nM1002 set_filament_type:{material_type, extruder_nr}\n\n; WIPE\nM400\nM106 P1 S178\nM400 S3\nG1 X-38.2 F18000\nG1 X-48.2 F3000\nG1 X-38.2 F18000\nG1 X-48.2 F3000\nG1 X-38.2 F18000\nG1 X-48.2 F3000\nM400\nM106 P1 S0\n\nM106 P1 S60\n; FLUSH_START\nG1 E{(material_flush_purge_length / 4.0) * 0.18} F{material_flush_purge_speed, extruder_nr}\nG1 E{(material_flush_purge_length / 4.0) * 0.02} F50\nG1 E{(material_flush_purge_length / 4.0) * 0.18} F{material_flush_purge_speed, extruder_nr}\nG1 E{(material_flush_purge_length / 4.0) * 0.02} F50\nG1 E{(material_flush_purge_length / 4.0) * 0.18} F{material_flush_purge_speed, extruder_nr}\nG1 E{(material_flush_purge_length / 4.0) * 0.02} F50\nG1 E{(material_flush_purge_length / 4.0) * 0.18} F{material_flush_purge_speed, extruder_nr}\nG1 E{(material_flush_purge_length / 4.0) * 0.02} F50\nG1 E{(material_flush_purge_length / 4.0) * 0.18} F{material_flush_purge_speed, extruder_nr}\nG1 E{(material_flush_purge_length / 4.0) * 0.02} F50\n; FLUSH_END\nG1 E-{retraction_amount * 2} F1800\nG1 E{retraction_amount * 2} F300\n\n; WIPE\nM400\nM106 P1 S178\nM400 S3\nG1 X-38.2 F18000\nG1 X-48.2 F3000\nG1 X-38.2 F18000\nG1 X-48.2 F3000\nG1 X-38.2 F18000\nG1 X-48.2 F3000\nM400\nM106 P1 S0\n\nM106 P1 S60\n; FLUSH_START\nG1 E{(material_flush_purge_length / 4.0) * 0.18} F{material_flush_purge_speed, extruder_nr}\nG1 E{(material_flush_purge_length / 4.0) * 0.02} F50\nG1 E{(material_flush_purge_length / 4.0) * 0.18} F{material_flush_purge_speed, extruder_nr}\nG1 E{(material_flush_purge_length / 4.0) * 0.02} F50\nG1 E{(material_flush_purge_length / 4.0) * 0.18} F{material_flush_purge_speed, extruder_nr}\nG1 E{(material_flush_purge_length / 4.0) * 0.02} F50\nG1 E{(material_flush_purge_length / 4.0) * 0.18} F{material_flush_purge_speed, extruder_nr}\nG1 E{(material_flush_purge_length / 4.0) * 0.02} F50\nG1 E{(material_flush_purge_length / 4.0) * 0.18} F{material_flush_purge_speed, extruder_nr}\nG1 E{(material_flush_purge_length / 4.0) * 0.02} F50\n; FLUSH_END\nG1 E-{retraction_amount * 2} F1800\nG1 E{retraction_amount * 2} F300\n\n; FLUSH_START\nM400\nM109 S{material_print_temperature}\nG1 E6 F{material_flush_purge_speed, extruder_nr} ;Compensate for filament spillage during waiting temperature\n; FLUSH_END\n\n{endif} ; prime_tower_enable\n\nM400\nG92 E0\n;G1 E-[new_retract_length_toolchange] F1800\nM106 P1 S255\nM400 S3\n\nG1 X70 F5000\nG1 X90 F3000\nG1 Y255 F4000\nG1 X105 F5000\nG1 Y265 F5000\nG1 X70 F10000\nG1 X100 F5000\nG1 X70 F10000\nG1 X100 F5000\n\nG1 X70 F10000\nG1 X80 F15000\nG1 X60\nG1 X80\nG1 X60\nG1 X80 ; shake to put down garbage\nG1 X100 F5000\nG1 X165 F15000; wipe and shake\nG1 Y256 ; move Y to aside, prevent collision\nM400\n\nM621 S{extruder_nr}A\n"}, "material_diameter": {"default_value": 1.75}}}